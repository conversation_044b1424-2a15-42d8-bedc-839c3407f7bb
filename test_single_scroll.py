#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单步滚动逻辑
功能：验证新的单次点击+单步滚动模式
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from contacts_coordinate_clicker import ContactsCoordinateClicker

def test_single_scroll_mode():
    """测试单步滚动模式"""
    print("🧪 测试单次点击+单步滚动模式")
    print("=" * 60)
    
    # 创建点击器
    clicker = ContactsCoordinateClicker()
    
    # 显示配置
    print("📋 配置信息:")
    print(f"  列表区域: ({clicker.contact_area['x']}, {clicker.contact_area['y']}, {clicker.contact_area['width']}, {clicker.contact_area['height']})")
    print(f"  固定点击位置: ({clicker.contact_area['x'] + clicker.click_offset_x}, {clicker.fixed_click_y})")
    print(f"  单步滚动距离: {clicker.single_contact_scroll} 步")
    
    # 检测窗口
    print("\n🔍 检测通讯录管理窗口...")
    hwnd = clicker.find_contacts_management_window()
    
    if not hwnd:
        print("❌ 未找到通讯录管理窗口")
        return False
    
    print(f"✅ 找到窗口 (句柄: {hwnd})")
    
    # 选择测试模式
    print("\n选择测试模式:")
    print("1. 仅测试前5个联系人（推荐）")
    print("2. 测试前10个联系人")
    print("3. 完整测试（最多200个联系人）")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        max_contacts = 5
    elif choice == "2":
        max_contacts = 10
    elif choice == "3":
        max_contacts = 200
    else:
        print("❌ 无效选择")
        return False
    
    print(f"\n🚀 开始测试，处理前 {max_contacts} 个联系人...")
    
    # 执行测试
    result = clicker.process_all_contacts_with_single_scroll(max_contacts=max_contacts)
    
    if result:
        print(f"\n✅ 测试成功完成")
    else:
        print(f"\n⚠️ 测试执行失败")
    
    return result

def show_comparison():
    """显示新旧模式对比"""
    print("\n📊 新旧模式对比:")
    print("=" * 60)
    print("| 特性 | 旧模式（批量+页面滚动） | 新模式（单次+单步滚动） |")
    print("|------|---------------------|---------------------|")
    print("| 点击方式 | 批量点击19个位置 | 固定位置单次点击 |")
    print("| 滚动方式 | 页面级滚动 | 单步精确滚动 |")
    print("| 重复风险 | 高（可能重复点击） | 低（避免重复） |")
    print("| 执行效率 | 中等 | 高 |")
    print("| 准确性 | 中等 | 高 |")
    print("| 可控性 | 低 | 高 |")
    
    print("\n🎯 新模式优势:")
    print("  ✅ 避免重复点击同一联系人")
    print("  ✅ 固定点击位置，减少坐标计算错误")
    print("  ✅ 精确单步滚动，确保不遗漏联系人")
    print("  ✅ 更好的进度控制和错误处理")

def main():
    """主函数"""
    print("🎯 单步滚动模式测试工具")
    print("=" * 60)
    
    show_comparison()
    
    # 询问是否进行测试
    test_confirm = input("\n是否进行实际测试? (y/N): ").strip().lower()
    
    if test_confirm == 'y':
        success = test_single_scroll_mode()
        
        if success:
            print("\n🎉 测试完成！新的单步滚动模式工作正常")
            print("现在可以使用 auto_click_contacts.py 进行完整的联系人处理")
        else:
            print("\n⚠️ 测试失败，请检查配置或窗口状态")
    else:
        print("\n📋 测试取消，仅显示配置信息")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
