#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动清理功能
验证wechat_contact_detector.py是否在启动时自动清理screenshots目录
"""

import os
import time
import shutil
from pathlib import Path

def create_test_images():
    """创建一些测试图像文件"""
    screenshots_dir = "screenshots"
    
    # 确保目录存在
    if not os.path.exists(screenshots_dir):
        os.makedirs(screenshots_dir)
    
    # 创建一些测试文件
    test_files = [
        "test_image1.png",
        "test_image2.jpg", 
        "debug_screenshot.png",
        "contact_detection.jpeg",
        "window_capture.bmp",
        "not_image.txt",  # 这个不应该被删除
        "config.json"     # 这个也不应该被删除
    ]
    
    created_files = []
    for filename in test_files:
        filepath = os.path.join(screenshots_dir, filename)
        try:
            with open(filepath, 'w') as f:
                if filename.endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                    f.write("fake image content")
                else:
                    f.write("test content")
            created_files.append(filename)
            print(f"✅ 创建测试文件: {filename}")
        except Exception as e:
            print(f"❌ 创建文件失败 {filename}: {e}")
    
    return created_files

def check_directory_contents():
    """检查目录内容"""
    screenshots_dir = "screenshots"
    
    if not os.path.exists(screenshots_dir):
        print("📁 screenshots目录不存在")
        return []
    
    files = os.listdir(screenshots_dir)
    print(f"📋 目录内容 ({len(files)} 个文件):")
    for file in files:
        print(f"  - {file}")
    
    return files

def test_auto_cleanup():
    """测试自动清理功能"""
    print("🧪 测试自动清理功能")
    print("=" * 50)
    
    # 步骤1：创建测试文件
    print("\n📝 步骤1: 创建测试文件")
    created_files = create_test_images()
    
    # 步骤2：检查创建的文件
    print("\n📋 步骤2: 检查创建的文件")
    files_before = check_directory_contents()
    
    # 步骤3：导入并创建检测器实例（应该触发自动清理）
    print("\n🔄 步骤3: 导入检测器（应该触发自动清理）")
    try:
        from wechat_contact_detector import WeChatContactDetector
        print("✅ 成功导入 WeChatContactDetector")
        
        # 创建实例（这会触发自动清理）
        print("🧹 创建检测器实例（触发自动清理）...")
        detector = WeChatContactDetector(debug_mode=False)
        print("✅ 检测器实例创建完成")
        
    except Exception as e:
        print(f"❌ 导入或创建检测器失败: {e}")
        return False
    
    # 步骤4：检查清理后的文件
    print("\n📋 步骤4: 检查清理后的文件")
    files_after = check_directory_contents()
    
    # 步骤5：分析清理结果
    print("\n📊 步骤5: 分析清理结果")
    
    # 应该被删除的文件（图像文件）
    expected_deleted = [f for f in created_files if f.endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
    # 应该保留的文件（非图像文件）
    expected_kept = [f for f in created_files if not f.endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
    
    print(f"📋 应该删除的文件 ({len(expected_deleted)} 个):")
    for file in expected_deleted:
        if file in files_after:
            print(f"  ❌ {file} (未删除)")
        else:
            print(f"  ✅ {file} (已删除)")
    
    print(f"\n📋 应该保留的文件 ({len(expected_kept)} 个):")
    for file in expected_kept:
        if file in files_after:
            print(f"  ✅ {file} (已保留)")
        else:
            print(f"  ❌ {file} (被误删)")
    
    # 计算成功率
    correctly_deleted = sum(1 for f in expected_deleted if f not in files_after)
    correctly_kept = sum(1 for f in expected_kept if f in files_after)
    
    total_expected = len(expected_deleted) + len(expected_kept)
    total_correct = correctly_deleted + correctly_kept
    
    success_rate = (total_correct / total_expected * 100) if total_expected > 0 else 100
    
    print(f"\n📊 清理结果统计:")
    print(f"  正确删除: {correctly_deleted}/{len(expected_deleted)} 个图像文件")
    print(f"  正确保留: {correctly_kept}/{len(expected_kept)} 个非图像文件")
    print(f"  总体成功率: {success_rate:.1f}%")
    
    # 判断测试是否成功
    test_passed = (correctly_deleted == len(expected_deleted) and 
                   correctly_kept == len(expected_kept))
    
    if test_passed:
        print("\n🎉 自动清理功能测试通过！")
        print("✅ 所有图像文件都被正确删除")
        print("✅ 所有非图像文件都被正确保留")
    else:
        print("\n❌ 自动清理功能测试失败！")
        if correctly_deleted < len(expected_deleted):
            print("⚠️ 部分图像文件未被删除")
        if correctly_kept < len(expected_kept):
            print("⚠️ 部分非图像文件被误删")
    
    return test_passed

def main():
    """主测试函数"""
    print("🧪 微信联系人检测器 - 自动清理功能测试")
    print("🎯 验证程序启动时是否自动清理screenshots目录中的图像文件")
    print("=" * 70)
    
    try:
        result = test_auto_cleanup()
        
        print("\n" + "=" * 70)
        print("📊 测试结果汇总")
        print("=" * 70)
        print(f"  自动清理功能: {'✅ 通过' if result else '❌ 失败'}")
        
        if result:
            print("\n🎉 所有测试通过！")
            print("✅ wechat_contact_detector.py 的自动清理功能正常工作")
            print("✅ 每次运行程序时都会自动清理调试图像")
        else:
            print("\n❌ 测试失败！")
            print("🔧 需要检查自动清理功能的实现")
        
        print("\n💡 提示:")
        print("  - 自动清理只删除图像文件(.png, .jpg, .jpeg, .bmp, .tiff)")
        print("  - 其他文件类型会被保留")
        print("  - 可以使用 'python wechat_contact_detector.py clean' 手动清理")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
