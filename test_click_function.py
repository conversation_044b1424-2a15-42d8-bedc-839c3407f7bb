#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试点击最后一个联系人功能
验证wechat_contact_detector.py的自动点击功能
"""

import logging
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_click_functionality():
    """测试点击功能"""
    print("🧪 测试点击最后一个联系人功能")
    print("=" * 50)
    
    try:
        # 导入检测器
        from wechat_contact_detector import WeChatContactDetector
        print("✅ 成功导入 WeChatContactDetector")
        
        # 创建检测器实例
        detector = WeChatContactDetector(debug_mode=True)
        print("✅ 成功创建检测器实例")
        
        # 测试1：检测最后一个联系人（不点击）
        print("\n🔍 测试1: 检测最后一个联系人（不点击）")
        screenshot = detector.capture_wechat_window()
        if screenshot is not None:
            contacts = detector._find_contacts_by_list_items(screenshot)
            if contacts:
                print(f"✅ 检测到 {len(contacts)} 个联系人")
                for i, contact in enumerate(contacts):
                    name = contact.get('name', f'联系人{i+1}')
                    coord = contact.get('coord', (0, 0))
                    source = contact.get('source', 'unknown')
                    print(f"  {i+1}. {name} - 坐标: {coord} - 来源: {source}")
                    if 'last' in source:
                        print(f"     ✅ 这是最后一个联系人")
            else:
                print("⚠️ 未检测到联系人")
        else:
            print("❌ 无法获取截图")
        
        # 测试2：验证点击方法存在
        print("\n🔍 测试2: 验证点击方法")
        if hasattr(detector, 'click_last_contact'):
            print("✅ click_last_contact 方法存在")
        else:
            print("❌ click_last_contact 方法不存在")
            
        if hasattr(detector, 'click_last_contact_auto'):
            print("✅ click_last_contact_auto 方法存在")
        else:
            print("❌ click_last_contact_auto 方法不存在")
        
        # 测试3：模拟点击（不实际执行）
        print("\n🔍 测试3: 模拟点击测试")
        if contacts:
            test_contact = contacts[0]
            print(f"📋 模拟点击联系人: {test_contact.get('name', '未知')}")
            print(f"📍 点击坐标: {test_contact.get('coord', (0, 0))}")
            print("ℹ️ 这是模拟测试，不会实际执行点击")
            
            # 验证联系人数据完整性
            required_fields = ['name', 'coord', 'confidence', 'source']
            missing_fields = []
            for field in required_fields:
                if field not in test_contact:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"⚠️ 联系人数据缺少字段: {missing_fields}")
            else:
                print("✅ 联系人数据完整")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_click_modes():
    """测试不同的点击模式"""
    print("\n🔧 测试不同的点击模式")
    print("=" * 50)
    
    modes = [
        ("contact", "联系人识别测试"),
        ("cleaned", "清理后检测测试"),
        ("click", "自动点击模式"),
        ("clean", "清理模式")
    ]
    
    print("📋 可用的运行模式:")
    for mode, description in modes:
        print(f"  python wechat_contact_detector.py {mode:<8} - {description}")
    
    print("\n💡 推荐测试流程:")
    print("  1. 先运行 'clean' 模式清理调试图像")
    print("  2. 运行 'cleaned' 模式检测联系人（选择不自动点击）")
    print("  3. 查看 screenshots/ 目录中的调试图像")
    print("  4. 确认检测正确后，运行 'click' 模式执行点击")

def main():
    """主测试函数"""
    print("🧪 微信联系人点击功能测试")
    print("🎯 验证 wechat_contact_detector.py 的自动点击功能")
    print("=" * 70)
    
    # 测试基本功能
    result = test_click_functionality()
    
    # 测试模式说明
    test_click_modes()
    
    print("\n" + "=" * 70)
    print("📊 测试结果汇总")
    print("=" * 70)
    print(f"  基本功能测试: {'✅ 通过' if result else '❌ 失败'}")
    
    if result:
        print("\n🎉 点击功能测试通过！")
        print("✅ 所有必要的方法和功能都已实现")
        print("✅ 联系人检测功能正常")
        print("✅ 点击方法已准备就绪")
        
        print("\n🚀 使用建议:")
        print("  1. 确保微信已打开并显示通讯录界面")
        print("  2. 使用 'python wechat_contact_detector.py click' 执行自动点击")
        print("  3. 或使用 'python wechat_contact_detector.py cleaned' 并选择自动点击")
    else:
        print("\n❌ 点击功能测试失败！")
        print("🔧 需要检查代码实现")
    
    print("\n💡 安全提示:")
    print("  - 自动点击会实际操作微信界面")
    print("  - 建议先在测试环境中验证")
    print("  - 可以先使用检测模式查看调试图像")

if __name__ == "__main__":
    main()
