#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联系人列表管理器测试脚本
功能：测试联系人列表的查找和操作功能
"""

import logging
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from contacts_list_manager import ContactsListManager

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_contacts_list.log', encoding='utf-8')
        ]
    )

def test_window_finding():
    """测试窗口查找功能"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试窗口查找功能")
    
    try:
        manager = ContactsListManager()
        
        # 测试查找通讯录管理窗口
        hwnd = manager.find_contacts_management_window()
        
        if hwnd:
            logger.info(f"✅ 找到通讯录管理窗口，句柄: {hwnd}")
            
            # 获取窗口信息
            import win32gui
            try:
                title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                rect = win32gui.GetWindowRect(hwnd)
                
                logger.info(f"📋 窗口标题: '{title}'")
                logger.info(f"📋 窗口类名: '{class_name}'")
                logger.info(f"📐 窗口位置: {rect}")
                
                return hwnd
            except Exception as info_error:
                logger.error(f"❌ 获取窗口信息失败: {info_error}")
                return None
        else:
            logger.warning("⚠️ 未找到通讯录管理窗口")
            return None
            
    except Exception as e:
        logger.error(f"❌ 窗口查找测试异常: {e}")
        return None

def test_list_finding(parent_hwnd):
    """测试联系人列表查找功能"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试联系人列表查找功能")
    
    try:
        manager = ContactsListManager()
        
        # 测试查找联系人列表元素
        list_hwnd = manager.find_contacts_list_element(parent_hwnd)
        
        if list_hwnd:
            logger.info(f"✅ 找到联系人列表元素，句柄: {list_hwnd}")
            
            # 获取列表信息
            import win32gui
            try:
                class_name = win32gui.GetClassName(list_hwnd)
                rect = win32gui.GetWindowRect(list_hwnd)
                
                logger.info(f"📋 列表类名: '{class_name}'")
                logger.info(f"📐 列表位置: {rect}")
                
                return list_hwnd
            except Exception as info_error:
                logger.error(f"❌ 获取列表信息失败: {info_error}")
                return None
        else:
            logger.warning("⚠️ 未找到联系人列表元素")
            return None
            
    except Exception as e:
        logger.error(f"❌ 列表查找测试异常: {e}")
        return None

def test_contact_items(list_hwnd):
    """测试联系人项目获取功能"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试联系人项目获取功能")
    
    try:
        manager = ContactsListManager()
        
        # 测试获取联系人项目
        contact_items = manager.get_contact_items(list_hwnd)
        
        if contact_items:
            logger.info(f"✅ 找到 {len(contact_items)} 个联系人项目")
            
            # 显示前5个联系人信息
            for i, contact in enumerate(contact_items[:5], 1):
                name = contact.get('name', '未知')
                center = contact.get('center', (0, 0))
                rect = contact.get('rect', (0, 0, 0, 0))
                
                logger.info(f"  {i}. '{name}' - 中心点: {center}, 边界: {rect}")
            
            if len(contact_items) > 5:
                logger.info(f"  ... 还有 {len(contact_items) - 5} 个联系人")
            
            return contact_items
        else:
            logger.warning("⚠️ 未找到任何联系人项目")
            return []
            
    except Exception as e:
        logger.error(f"❌ 联系人项目获取测试异常: {e}")
        return []

def test_click_simulation(contact_items):
    """测试点击模拟功能（不实际点击）"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试点击模拟功能")
    
    try:
        if not contact_items:
            logger.warning("⚠️ 没有联系人项目可供测试")
            return False
        
        # 模拟点击前3个联系人
        test_count = min(3, len(contact_items))
        logger.info(f"📋 模拟点击前 {test_count} 个联系人...")
        
        for i, contact in enumerate(contact_items[:test_count], 1):
            name = contact.get('name', f'联系人{i}')
            center = contact.get('center', (0, 0))
            
            logger.info(f"  {i}. 模拟点击 '{name}' 坐标: {center}")
            # 这里不实际点击，只是模拟
            
        logger.info("✅ 点击模拟测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 点击模拟测试异常: {e}")
        return False

def main():
    """主测试函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🧪 联系人列表管理器测试")
    print("=" * 60)
    
    try:
        # 显示UI树信息
        print("📋 基于UI树信息:")
        print("  ClassName: mmui::ContactsManagerDetailView")
        print("  ControlType: ControlType.List")
        print("  BoundingRectangle: (1400, 76, 520, 924)")
        print("  ControlPatterns: Invoke, Selection, Value")
        
        # 测试1: 窗口查找
        print("\n🧪 测试1: 查找通讯录管理窗口")
        parent_hwnd = test_window_finding()
        
        if not parent_hwnd:
            print("❌ 窗口查找失败，无法继续测试")
            return
        
        # 测试2: 列表查找
        print("\n🧪 测试2: 查找联系人列表元素")
        list_hwnd = test_list_finding(parent_hwnd)
        
        if not list_hwnd:
            print("❌ 列表查找失败，无法继续测试")
            return
        
        # 测试3: 联系人项目获取
        print("\n🧪 测试3: 获取联系人项目")
        contact_items = test_contact_items(list_hwnd)
        
        # 测试4: 点击模拟
        print("\n🧪 测试4: 点击模拟")
        test_click_simulation(contact_items)
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        print("=" * 60)
        
        if parent_hwnd:
            print("✅ 通讯录管理窗口查找: 成功")
        else:
            print("❌ 通讯录管理窗口查找: 失败")
        
        if list_hwnd:
            print("✅ 联系人列表元素查找: 成功")
        else:
            print("❌ 联系人列表元素查找: 失败")
        
        if contact_items:
            print(f"✅ 联系人项目获取: 成功 (找到 {len(contact_items)} 个)")
        else:
            print("❌ 联系人项目获取: 失败")
        
        print("✅ 点击模拟测试: 完成")
        
        if parent_hwnd and list_hwnd and contact_items:
            print("\n🎉 所有测试通过！联系人列表管理器功能正常")
        else:
            print("\n⚠️ 部分测试失败，请检查通讯录管理窗口是否已打开")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        logger.info("⚠️ 用户中断测试")
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        print(f"\n❌ 测试程序异常: {e}")
    
    print("=" * 60)
    print("📋 详细日志已保存到: test_contacts_list.log")

if __name__ == "__main__":
    main()
