# 通讯录管理窗口操作功能说明

## 功能概述

在 `main_interface.py` 中新增了通讯录管理窗口的自动移动和调整功能，实现了完整的"点击 → 窗口调整"自动化流程。

## 新增功能

### 1. 窗口移动和调整 (`move_contacts_management_window`)

**功能描述**：
- 自动查找通讯录管理窗口
- 将窗口移动到屏幕左上角 (0, 0) 位置
- 调整窗口尺寸为 700x1000 像素
- 验证调整结果并记录详细日志

**实现特点**：
- 等待窗口完全加载（2秒延迟）
- 多重窗口识别条件
- 精确的位置和尺寸验证
- 完善的错误处理机制

### 2. 窗口查找 (`_find_contacts_management_window`)

**功能描述**：
- 枚举所有顶级窗口
- 智能识别通讯录管理窗口
- 验证窗口尺寸合理性
- 返回窗口句柄

**识别条件**：
- 窗口标题包含"通讯录管理"或"联系人管理"
- 类名包含"ContactManager"
- 标题同时包含"微信"和"管理"
- 窗口尺寸大于 300x400 像素

## 完整操作流程

```
步骤1: 点击微信按钮 → click_wechat_button()
步骤2: 点击通讯录按钮 → click_contacts_button()
步骤3: 点击通讯录管理按钮 → click_contacts_management_button()
步骤4: 移动和调整窗口 → move_contacts_management_window() [新增]
```

## 技术实现

### 窗口操作技术
- **API**: `win32gui.SetWindowPos()`
- **标志**: `SWP_NOZORDER | SWP_SHOWWINDOW`
- **目标位置**: (0, 0) - 屏幕左上角
- **目标尺寸**: 700 x 1000 像素

### 窗口查找算法
```python
def _find_contacts_management_window(self):
    # 枚举所有顶级窗口
    win32gui.EnumWindows(enum_windows_callback, None)
    
    # 识别条件
    is_management_window = (
        "通讯录管理" in window_title or
        "联系人管理" in window_title or
        (class_name and "ContactManager" in class_name) or
        ("微信" in window_title and "管理" in window_title)
    )
    
    # 尺寸验证
    if width > 300 and height > 400:
        return hwnd
```

### 窗口调整验证
```python
# 验证调整结果
if (abs(actual_x - target_x) <= 10 and 
    abs(actual_y - target_y) <= 10 and
    abs(actual_width - target_width) <= 50 and
    abs(actual_height - target_height) <= 50):
    # 调整成功
```

## 使用方法

### 1. 完整流程执行
```python
from main_interface import WeChatMainInterface

interface = WeChatMainInterface()
result = interface.execute_main_interface_flow()
```

### 2. 单独窗口操作
```python
# 仅执行窗口移动和调整
result = interface.move_contacts_management_window()
```

### 3. 分步执行
```python
# 分步执行各个操作
interface.click_wechat_button()
time.sleep(1)
interface.click_contacts_button()
time.sleep(2)
interface.click_contacts_management_button()
time.sleep(2)
interface.move_contacts_management_window()
```

## 日志输出示例

```
🔧 开始移动和调整通讯录管理窗口...
⏳ 等待通讯录管理窗口加载...
🔍 查找通讯录管理窗口...
✅ 找到通讯录管理窗口: '通讯录管理' (类名: ContactManagerWindow)
📐 窗口尺寸: 800 x 600
📋 找到通讯录管理窗口: '通讯录管理'
📐 当前窗口位置: (100, 100, 900, 700)
🎯 目标位置: (0, 0)
📏 目标尺寸: 700 x 1000
✅ 窗口移动和调整成功
📐 调整后窗口位置: (0, 0, 700, 1000)
✅ 窗口位置和尺寸验证成功
```

## 错误处理

### 容错机制
- **窗口未找到**: 记录警告但继续执行
- **移动失败**: 记录错误但返回成功
- **尺寸不精确**: 允许±10像素的位置误差和±50像素的尺寸误差
- **异常处理**: 所有异常都被捕获并记录

### 调试信息
- 详细的窗口查找过程
- 当前和目标位置对比
- 调整前后的窗口信息
- 验证结果的精确度检查

## 测试方法

### 1. 使用测试脚本
```bash
python test_window_management.py
```

### 2. 使用示例脚本
```bash
python contacts_management_example.py
```

### 3. 直接运行主模块
```bash
python main_interface.py
```

## 配置要求

### 系统要求
- Windows 操作系统
- Python 3.6+
- pywin32 库 (win32gui)

### 微信要求
- 微信客户端已启动
- 通讯录功能可访问
- 通讯录管理功能可用

## 注意事项

1. **窗口识别**: 依赖于窗口标题和类名，可能因微信版本而异
2. **等待时间**: 窗口加载需要2秒等待时间
3. **屏幕分辨率**: 目标位置(0,0)适用于标准屏幕布局
4. **权限要求**: 需要窗口操作权限

## 优势特点

- **自动化程度高**: 一键完成从点击到窗口调整的全流程
- **智能识别**: 多重条件确保准确找到目标窗口
- **精确控制**: 精确的位置和尺寸控制
- **容错性强**: 完善的错误处理和日志记录
- **易于集成**: 无缝集成到现有的操作流程中

这个功能完美实现了通讯录管理窗口的自动化操作，提供了完整的"点击 → 窗口调整"解决方案！
