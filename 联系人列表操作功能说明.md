# 联系人列表操作功能说明

## 功能概述

基于提供的UI树信息，创建了专门的联系人列表操作脚本，能够自动获取通讯录管理窗口中的所有联系人并循环点击每个联系人。

## UI树信息分析

### 联系人列表元素属性
```
ClassName: mmui::ContactsManagerDetailView
ControlType: ControlType.List
BoundingRectangle: (1400, 76, 520, 924)
ControlPatterns: Invoke, Selection, Value
IsKeyboardFocusable: True
LocalizedControlType: 列表
```

### 关键信息
- **位置**: (1400, 76) - 列表左上角坐标
- **尺寸**: 520 x 924 像素
- **类型**: 列表控件，支持选择和调用操作
- **可交互**: 支持键盘焦点和鼠标点击

## 核心功能模块

### 1. ContactsListManager 类

**主要功能**:
- 查找通讯录管理窗口
- 定位联系人列表元素
- 获取所有联系人项目
- 循环点击每个联系人

### 2. 核心方法

#### `find_contacts_management_window()`
- **功能**: 查找通讯录管理窗口句柄
- **识别条件**: 窗口标题包含"通讯录管理"、"联系人管理"等
- **验证**: 检查窗口尺寸合理性

#### `find_contacts_list_element(parent_hwnd)`
- **功能**: 在管理窗口中查找联系人列表元素
- **匹配条件**: 类名包含 `mmui::ContactsManagerDetailView`
- **位置验证**: 检查边界矩形是否与UI树信息匹配

#### `get_contact_items(list_hwnd)`
- **功能**: 获取列表中的所有联系人项目
- **过滤条件**: 排除系统元素，只保留有效联系人
- **排序**: 按Y坐标从上到下排序

#### `click_contact_item(contact_info)`
- **功能**: 点击指定的联系人项目
- **操作**: 移动鼠标到中心点并执行点击
- **延迟**: 点击后等待0.5秒响应

#### `process_all_contacts()`
- **功能**: 执行完整的联系人处理流程
- **流程**: 查找窗口 → 定位列表 → 获取项目 → 循环点击

## 使用方法

### 1. 单独使用联系人列表管理器
```bash
python contacts_list_manager.py
```

### 2. 使用完整工作流程
```bash
python complete_contacts_workflow.py
```

### 3. 测试功能
```bash
python test_contacts_list.py
```

## 完整工作流程

### 集成流程 (complete_contacts_workflow.py)
```
阶段1: 主界面操作
  步骤1: 点击微信按钮
  步骤2: 点击通讯录按钮
  步骤3: 点击通讯录管理按钮
  步骤4: 移动窗口到右上角并调整尺寸

阶段2: 联系人列表操作
  步骤5: 查找通讯录管理窗口
  步骤6: 定位联系人列表元素
  步骤7: 获取所有联系人项目
  步骤8: 循环点击每个联系人
```

## 技术实现

### 窗口查找算法
```python
# 枚举所有顶级窗口
win32gui.EnumWindows(enum_windows_callback, None)

# 识别条件
is_management_window = (
    "通讯录管理" in window_title or
    "联系人管理" in window_title or
    (class_name and "ContactManager" in class_name) or
    ("微信" in window_title and "管理" in window_title)
)
```

### 列表元素定位
```python
# 匹配类名
if self.list_class_name in class_name:
    # 验证边界矩形
    if (abs(x - expected_x) <= 50 and abs(y - expected_y) <= 50 and
        abs(width - expected_w) <= 100 and abs(height - expected_h) <= 100):
        return hwnd
```

### 联系人项目过滤
```python
# 检查文本内容和窗口尺寸
if (1 <= len(text) <= 50 and 
    50 <= width <= 500 and 20 <= height <= 100 and
    expected_x <= x <= expected_x + expected_w):
    return True
```

## 配置参数

### 位置配置
- **列表区域**: (1400, 76, 520, 924)
- **位置容差**: ±50像素
- **尺寸容差**: ±100像素

### 点击配置
- **移动速度**: 0.3秒
- **点击延迟**: 0.5秒
- **操作间隔**: 1.0秒

### 过滤条件
- **文本长度**: 1-50字符
- **项目宽度**: 50-500像素
- **项目高度**: 20-100像素

## 日志输出示例

```
🔍 查找通讯录管理窗口...
✅ 找到通讯录管理窗口: '通讯录管理' (类名: ContactManagerWindow)
🔍 查找联系人列表元素...
✅ 联系人列表位置验证通过
📋 获取联系人列表项目...
✅ 找到 15 个联系人项目
📋 开始循环点击 15 个联系人...
🖱️ 点击联系人: '张三' 坐标: (1550, 150)
✅ 成功点击联系人: '张三'
🖱️ 点击联系人: '李四' 坐标: (1550, 200)
✅ 成功点击联系人: '李四'
...
✅ 联系人处理完成: 成功 15/15
```

## 错误处理

### 容错机制
- **窗口未找到**: 记录错误并返回失败
- **列表元素缺失**: 尝试多种匹配条件
- **联系人为空**: 记录警告但不中断流程
- **点击失败**: 记录失败但继续下一个联系人

### 调试功能
- **详细日志**: 记录每个步骤的执行情况
- **位置验证**: 检查元素位置是否符合预期
- **项目统计**: 显示找到的联系人数量和成功率

## 注意事项

1. **窗口状态**: 确保通讯录管理窗口已打开并可见
2. **位置精度**: UI树坐标可能因微信版本而略有差异
3. **操作速度**: 点击间隔可根据系统性能调整
4. **权限要求**: 需要窗口操作和鼠标控制权限

## 优势特点

- **精确定位**: 基于UI树信息的精确元素定位
- **智能过滤**: 自动过滤系统元素，只保留有效联系人
- **容错性强**: 完善的错误处理和恢复机制
- **详细反馈**: 提供完整的操作过程日志
- **模块化设计**: 可单独使用或集成到完整流程中

这个联系人列表操作功能完美实现了基于UI树信息的自动化联系人处理，提供了高效、可靠的解决方案！
