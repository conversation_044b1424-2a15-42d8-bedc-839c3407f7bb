#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通讯录管理窗口操作测试脚本
功能：测试新添加的窗口移动和调整功能
"""

import logging
import time
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from main_interface import WeChatMainInterface

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_window_management.log', encoding='utf-8')
        ]
    )

def test_window_management_only():
    """仅测试窗口管理功能"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 开始测试窗口管理功能")
    
    try:
        interface = WeChatMainInterface()
        
        logger.info("🔧 测试窗口查找和移动功能...")
        
        # 直接测试窗口移动功能
        result = interface.move_contacts_management_window()
        
        if result:
            logger.info("✅ 窗口管理功能测试成功")
            return True
        else:
            logger.warning("⚠️ 窗口管理功能测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 窗口管理功能测试异常: {e}")
        return False

def test_complete_workflow():
    """测试完整的工作流程"""
    logger = logging.getLogger(__name__)
    logger.info("🚀 开始测试完整的工作流程")
    
    try:
        interface = WeChatMainInterface()
        
        logger.info("📋 执行完整流程:")
        logger.info("  步骤1: 点击微信按钮")
        logger.info("  步骤2: 点击通讯录按钮")
        logger.info("  步骤3: 点击通讯录管理按钮")
        logger.info("  步骤4: 移动和调整通讯录管理窗口(右上角)")
        
        # 执行完整流程
        result = interface.execute_main_interface_flow()
        
        if result:
            logger.info("✅ 完整工作流程测试成功")
            return True
        else:
            logger.warning("⚠️ 完整工作流程测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 完整工作流程测试异常: {e}")
        return False

def test_window_finding():
    """测试窗口查找功能"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 开始测试窗口查找功能")
    
    try:
        interface = WeChatMainInterface()
        
        # 测试查找通讯录管理窗口
        logger.info("🔍 测试查找通讯录管理窗口...")
        hwnd = interface._find_contacts_management_window()
        
        if hwnd:
            logger.info(f"✅ 找到通讯录管理窗口，句柄: {hwnd}")
            
            # 获取窗口信息
            import win32gui
            try:
                title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                rect = win32gui.GetWindowRect(hwnd)
                
                logger.info(f"📋 窗口标题: '{title}'")
                logger.info(f"📋 窗口类名: '{class_name}'")
                logger.info(f"📐 窗口位置: {rect}")
                
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]
                logger.info(f"📏 窗口尺寸: {width} x {height}")
                
                return True
            except Exception as info_error:
                logger.error(f"❌ 获取窗口信息失败: {info_error}")
                return False
        else:
            logger.warning("⚠️ 未找到通讯录管理窗口")
            return False
            
    except Exception as e:
        logger.error(f"❌ 窗口查找功能测试异常: {e}")
        return False

def main():
    """主测试函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("通讯录管理窗口操作测试")
    print("=" * 60)
    
    # 显示测试选项
    print("\n测试选项:")
    print("1. 仅测试窗口管理功能")
    print("2. 测试完整工作流程")
    print("3. 测试窗口查找功能")
    print("4. 运行所有测试")
    
    choice = input("\n请选择测试选项 (1-4): ").strip()
    
    results = []
    
    if choice == "1":
        logger.info("选择: 仅测试窗口管理功能")
        result = test_window_management_only()
        results.append(("窗口管理功能", result))
    elif choice == "2":
        logger.info("选择: 测试完整工作流程")
        result = test_complete_workflow()
        results.append(("完整工作流程", result))
    elif choice == "3":
        logger.info("选择: 测试窗口查找功能")
        result = test_window_finding()
        results.append(("窗口查找功能", result))
    elif choice == "4":
        logger.info("选择: 运行所有测试")
        
        print("\n🧪 测试1: 窗口查找功能")
        result1 = test_window_finding()
        results.append(("窗口查找功能", result1))
        
        print("\n🧪 测试2: 窗口管理功能")
        result2 = test_window_management_only()
        results.append(("窗口管理功能", result2))
        
        print("\n🧪 测试3: 完整工作流程")
        result3 = test_complete_workflow()
        results.append(("完整工作流程", result3))
    else:
        logger.warning("无效选择，默认运行窗口管理功能测试")
        result = test_window_management_only()
        results.append(("窗口管理功能", result))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试完成 - 全部成功")
        logger.info("🎉 所有测试完成 - 全部成功")
    else:
        print("\n⚠️ 测试完成 - 部分失败")
        logger.warning("⚠️ 测试完成 - 部分失败")
    
    print("=" * 60)
    
    # 提示查看日志
    print(f"\n📋 详细日志已保存到: test_window_management.log")

if __name__ == "__main__":
    main()
