#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信联系人检测器 - 合并版本

合并了detection.py和friend.py的所有功能，专注于识别微信通讯录界面中的最后一个联系人。

主要功能：
- 自动检测微信窗口并截图
- 多种联系人识别方法（轮廓检测、模板匹配、OCR）
- 专门的联系人列表检测，重点识别最后一个联系人
- 完整的调试和测试功能
- 智能点击执行联系人选择

作者: AI Assistant (合并版本)
创建时间: 2025-08-05
版本: 3.0.0 (合并版)
"""

import time
import logging
import cv2
import numpy as np
import pyautogui
import pygetwindow as gw
from typing import Optional, Tuple, List, Dict
import os
from datetime import datetime
import re
import sys
from pathlib import Path

# OCR相关导入
try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logging.warning("⚠️ pytesseract未安装，OCR功能不可用")

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    logging.warning("⚠️ easyocr未安装，EasyOCR功能不可用")

# 配置pyautogui
pyautogui.FAILSAFE = True  # 鼠标移到屏幕左上角时停止
pyautogui.PAUSE = 0.5      # 每次操作间隔0.5秒

class WeChatContactDetector:
    """微信联系人检测器 - 合并版本，专注于识别最后一个联系人"""
    
    def __init__(self, debug_mode: bool = True):
        """
        初始化微信联系人检测器
        
        Args:
            debug_mode: 是否开启调试模式
        """
        self.debug_mode = debug_mode
        self.setup_logging()
        self.screenshot_dir = "screenshots"
        self.ensure_screenshot_dir()

        # 🧹 程序启动时自动清理screenshots目录
        self.clean_screenshots_directory()
        
        # 微信窗口相关配置
        self.wechat_window_titles = [
            "微信",
            "WeChat",
            "添加朋友",
            "Add Friends",
            "添加联系人",
            "Add Contact"
        ]
        
        # 按钮识别相关配置
        self.button_texts = [
            "联系人",
            "Add to Contacts",
            "联系",
            "Add"
        ]
        
        self.logger.info("微信联系人检测器初始化完成")
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = logging.DEBUG if self.debug_mode else logging.INFO
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 设置控制台日志
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # 设置文件日志
        file_handler = logging.FileHandler(
            f'wechat_contact_detector_{datetime.now().strftime("%Y%m%d")}.log',
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        # 配置logger
        self.logger = logging.getLogger('WeChatContactDetector')
        self.logger.setLevel(log_level)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def ensure_screenshot_dir(self):
        """确保截图目录存在"""
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
            self.logger.info(f"创建截图目录: {self.screenshot_dir}")
    
    def clean_screenshots_directory(self, show_details: bool = True):
        """
        清理screenshots目录下的所有图片文件

        Args:
            show_details: 是否显示详细的清理信息
        """
        try:
            if not os.path.exists(self.screenshot_dir):
                os.makedirs(self.screenshot_dir)
                if show_details:
                    self.logger.info(f"📁 创建screenshots目录: {self.screenshot_dir}")
                return

            # 获取所有图片文件
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
            files_to_remove = []

            for file in os.listdir(self.screenshot_dir):
                file_path = os.path.join(self.screenshot_dir, file)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file.lower())
                    if ext in image_extensions:
                        files_to_remove.append(file_path)

            # 删除图片文件
            removed_count = 0
            failed_count = 0
            for file_path in files_to_remove:
                try:
                    os.remove(file_path)
                    removed_count += 1
                    if show_details:
                        filename = os.path.basename(file_path)
                        self.logger.debug(f"🗑️ 删除: {filename}")
                except Exception as e:
                    failed_count += 1
                    if show_details:
                        self.logger.warning(f"❌ 删除文件失败 {file_path}: {e}")

            # 显示清理结果
            if removed_count > 0:
                if show_details:
                    self.logger.info(f"🧹 清理screenshots目录完成: 删除了 {removed_count} 个图片文件")
                    if failed_count > 0:
                        self.logger.warning(f"⚠️ {failed_count} 个文件删除失败")
                else:
                    # 简化模式，只在控制台显示
                    print(f"🧹 清理调试图像: 删除了 {removed_count} 个文件")
            else:
                if show_details:
                    self.logger.info("✨ screenshots目录已经是干净的")

        except Exception as e:
            if show_details:
                self.logger.error(f"❌ 清理screenshots目录失败: {e}")
            else:
                print(f"❌ 清理调试图像失败: {e}")

    # ==================== 微信窗口检测功能 ====================
    
    def find_wechat_windows(self) -> List[gw.Win32Window]:
        """查找所有微信相关窗口"""
        wechat_windows = []
        all_windows = gw.getAllWindows()

        for window in all_windows:
            if window.title and any(title in window.title for title in self.wechat_window_titles):
                # 兼容不同版本的pygetwindow库
                is_visible = self._check_window_visible(window)
                if is_visible and window.width > 100 and window.height > 100:
                    wechat_windows.append(window)
                    self.logger.debug(f"找到微信窗口: {window.title} - {window.width}x{window.height}")

        self.logger.info(f"共找到 {len(wechat_windows)} 个微信窗口")
        return wechat_windows

    def _check_window_visible(self, window) -> bool:
        """检查窗口是否可见（兼容不同版本的pygetwindow）"""
        try:
            # 尝试使用 isVisible 属性
            if hasattr(window, 'isVisible'):
                return window.isVisible
            # 尝试使用 visible 属性
            elif hasattr(window, 'visible'):
                return window.visible
            # 如果都没有，假设窗口可见
            else:
                self.logger.warning(f"无法检查窗口可见性，假设窗口可见: {window.title}")
                return True
        except Exception as e:
            self.logger.warning(f"检查窗口可见性时发生错误: {e}")
            return True

    # ==================== 测试功能（来自detection.py）====================
    
    def test_cleaned_detection(self, auto_click: bool = True):
        """测试清理后的联系人检测功能（来自detection.py）"""
        try:
            print("🧹 清理后的联系人检测测试")
            print("=" * 50)
            print("🎯 只标记绿色粗框：选中的最后一个条目（最下方）")
            if auto_click:
                print("🖱️ 检测到最后一个联系人后将自动点击")

            # 获取截图
            print("\n📸 获取截图...")
            screenshot = self.capture_wechat_window()

            if screenshot is None:
                print("❌ 无法获取截图")
                return False

            height, width = screenshot.shape[:2]
            print(f"✅ 截图成功: {width}x{height}")

            # 测试联系人列表检测（只返回最后一个）
            print("\n🎯 执行联系人列表检测（只返回最后一个）...")
            contacts = self._find_contacts_by_list_items(screenshot)
            print(f"📊 检测结果: {len(contacts)} 个联系人条目")

            if contacts:
                print(f"\n📋 检测到的联系人条目:")
                last_contact = None
                for i, contact in enumerate(contacts):
                    name = contact.get('name', f'联系人{i+1}')
                    coord = contact.get('coord', (0, 0))
                    confidence = contact.get('confidence', 0)
                    source = contact.get('source', 'unknown')
                    bbox = contact.get('bbox', (0, 0, 0, 0))

                    print(f"  {i+1}. {name}")
                    print(f"     坐标: {coord}")
                    print(f"     置信度: {confidence}%")
                    print(f"     来源: {source}")
                    print(f"     边界框: {bbox}")

                    if 'last' in source:
                        print(f"     ✅ 确认为最后一个条目")
                        last_contact = contact

                # 如果启用自动点击且找到最后一个联系人，则执行点击
                if auto_click and last_contact:
                    print(f"\n🖱️ 准备点击最后一个联系人...")
                    click_success = self.click_last_contact(last_contact)
                    if click_success:
                        print("✅ 成功点击最后一个联系人！")
                    else:
                        print("❌ 点击最后一个联系人失败")

            else:
                print("⚠️ 未检测到联系人条目")

            # 测试完整识别流程
            print(f"\n🎯 测试完整识别流程...")
            all_contacts = self.find_contacts_in_screenshot(screenshot)
            print(f"📊 完整识别结果: {len(all_contacts)} 个联系人")

            if all_contacts:
                print(f"\n📋 完整识别详情:")
                for i, contact in enumerate(all_contacts):
                    name = contact.get('name', f'联系人{i+1}')
                    coord = contact.get('coord', (0, 0))
                    confidence = contact.get('confidence', 0)
                    source = contact.get('source', 'unknown')

                    print(f"  {i+1}. {name}")
                    print(f"     坐标: {coord}")
                    print(f"     置信度: {confidence}%")
                    print(f"     来源: {source}")

            return len(contacts) > 0

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    # ==================== 核心截图功能 ====================

    def capture_wechat_window(self) -> Optional[np.ndarray]:
        """获取微信主窗口截图 - 优化版"""
        try:
            self.logger.info("🔍 开始优化的微信窗口截图流程...")

            # 步骤1：使用window_manager确保微信窗口激活
            if not self._ensure_wechat_window_active():
                self.logger.error("❌ 无法激活微信窗口")
                return None

            # 步骤2：等待窗口稳定
            self._wait_for_window_stable()

            # 步骤3：获取精确的客户端区域截图
            screenshot = self._capture_client_area_screenshot()
            if screenshot is not None:
                # 步骤4：验证截图质量
                if self._validate_screenshot_quality(screenshot):
                    self.logger.info("✅ 成功获取高质量微信窗口截图")

                    # 保存调试截图
                    if self.debug_mode:
                        self._save_debug_screenshot(screenshot, "optimized_wechat_window")

                    return screenshot
                else:
                    self.logger.warning("⚠️ 截图质量验证失败，尝试重新截图")
                    # 重试一次
                    time.sleep(0.5)
                    screenshot = self._capture_client_area_screenshot()
                    if screenshot is not None and self._validate_screenshot_quality(screenshot):
                        self.logger.info("✅ 重试截图成功")

                        # 保存调试截图
                        if self.debug_mode:
                            self._save_debug_screenshot(screenshot, "optimized_wechat_window_retry")

                        return screenshot

            self.logger.error("❌ 微信窗口截图失败")
            return None

        except Exception as e:
            self.logger.error(f"❌ 获取微信窗口截图失败: {e}")
            return None

    def _ensure_wechat_window_active(self) -> bool:
        """使用window_manager确保微信窗口激活"""
        try:
            self.logger.info("🔄 使用window_manager激活微信窗口...")

            # 导入window_manager模块
            try:
                from window_manager import WeChatWindowManager
                window_manager = WeChatWindowManager()

                # 查找微信窗口
                windows = window_manager.find_all_wechat_windows()
                if not windows:
                    self.logger.error("❌ 未找到任何微信窗口")
                    return False

                # 选择主窗口
                main_window = None
                for window in windows:
                    if window.get('is_main', False) or '微信' in window.get('title', ''):
                        main_window = window
                        break

                if not main_window:
                    main_window = windows[0]  # 使用第一个窗口

                hwnd = main_window.get('hwnd')
                if hwnd is None:
                    self.logger.error("❌ 无法获取微信窗口句柄")
                    return False

                self.logger.info(f"🎯 选择微信窗口: {main_window.get('title', '未知')} (句柄: {hwnd})")

                # 激活窗口
                success = window_manager.activate_window(hwnd)
                if success:
                    self.logger.info("✅ 微信窗口激活成功")

                    # 验证窗口是否在前台
                    import win32gui
                    foreground_hwnd = win32gui.GetForegroundWindow()
                    if foreground_hwnd == hwnd:
                        self.logger.info("✅ 微信窗口已在前台")
                        return True
                    else:
                        self.logger.warning("⚠️ 微信窗口激活但未在前台，尝试强制置前")
                        # 强制置前
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.3)
                        return win32gui.GetForegroundWindow() == hwnd
                else:
                    self.logger.error("❌ 微信窗口激活失败")
                    return False

            except ImportError:
                self.logger.warning("⚠️ 无法导入window_manager，使用备选方案")
                return self._fallback_activate_wechat()

        except Exception as e:
            self.logger.error(f"❌ 激活微信窗口异常: {e}")
            return self._fallback_activate_wechat()

    def _fallback_activate_wechat(self) -> bool:
        """备选的微信窗口激活方案"""
        try:
            # 使用pygetwindow激活
            wechat_windows = gw.getWindowsWithTitle('微信')
            if not wechat_windows:
                return False

            main_window = wechat_windows[0]
            main_window.activate()
            time.sleep(0.5)

            # 验证激活状态
            if hasattr(main_window, 'isActive') and main_window.isActive:
                self.logger.info("✅ 备选方案激活微信窗口成功")
                return True
            else:
                self.logger.warning("⚠️ 备选方案激活失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 备选激活方案失败: {e}")
            return False

    def _wait_for_window_stable(self):
        """等待窗口稳定"""
        try:
            self.logger.info("⏳ 等待微信窗口稳定...")
            # 等待窗口动画完成和界面稳定
            time.sleep(1.0)
        except Exception as e:
            self.logger.warning(f"⚠️ 窗口稳定性检查异常: {e}")

    def _capture_client_area_screenshot(self) -> Optional[np.ndarray]:
        """获取精确的客户端区域截图"""
        try:
            self.logger.info("📸 开始精确客户端区域截图...")

            # 获取微信窗口
            wechat_windows = gw.getWindowsWithTitle('微信')
            if not wechat_windows:
                self.logger.warning("⚠️ 未找到微信窗口")
                return None

            main_window = wechat_windows[0]
            self.logger.info(f"📱 微信窗口信息: 位置({main_window.left}, {main_window.top}) 尺寸{main_window.width}x{main_window.height}")

            # 使用窗口整体区域，但排除边框
            border_offset = 8  # 估计的边框宽度
            title_height = 30  # 估计的标题栏高度

            # 计算实际内容区域
            content_left = main_window.left + border_offset
            content_top = main_window.top + title_height
            content_width = main_window.width - 2 * border_offset
            content_height = main_window.height - title_height - border_offset

            self.logger.info(f"📐 截图区域: ({content_left}, {content_top}) 尺寸: {content_width}x{content_height}")

            if content_width > 100 and content_height > 100:
                screenshot = pyautogui.screenshot(region=(content_left, content_top, content_width, content_height))
                screenshot_np = np.array(screenshot)
                screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                self.logger.info("✅ 截图完成")
                return screenshot_bgr
            else:
                self.logger.warning(f"⚠️ 截图区域尺寸异常: {content_width}x{content_height}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 精确截图失败: {e}")
            return None

    def _validate_screenshot_quality(self, screenshot: np.ndarray) -> bool:
        """验证截图质量"""
        try:
            if screenshot is None or screenshot.size == 0:
                return False

            height, width = screenshot.shape[:2]

            # 检查尺寸合理性
            if width < 400 or height < 300:
                self.logger.warning(f"⚠️ 截图尺寸过小: {width}x{height}")
                return False

            # 检查图像是否过于模糊
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()

            if laplacian_var < 50:  # 模糊阈值
                self.logger.warning(f"⚠️ 截图可能模糊，清晰度: {laplacian_var:.2f}")
                return False

            # 检查是否为纯色图像
            std_dev = cv2.meanStdDev(gray)[1][0][0]  # 使用OpenCV计算标准差
            if std_dev < 10:
                self.logger.warning(f"⚠️ 截图可能为纯色，标准差: {std_dev:.2f}")
                return False

            self.logger.info(f"✅ 截图质量验证通过: 尺寸{width}x{height}, 清晰度{laplacian_var:.2f}, 标准差{std_dev:.2f}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 截图质量验证异常: {e}")
            return False

    def _save_debug_screenshot(self, screenshot: np.ndarray, prefix: str):
        """保存调试截图"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_path = os.path.join(self.screenshot_dir, f"{prefix}_{timestamp}.png")
            cv2.imwrite(debug_path, screenshot)
            self.logger.debug(f"💾 保存调试截图: {debug_path}")
        except Exception as e:
            self.logger.warning(f"⚠️ 保存调试截图失败: {e}")

    # ==================== 核心联系人识别功能 ====================

    def find_contacts_in_screenshot(self, screenshot: np.ndarray) -> List[Dict]:
        """
        在截图中识别联系人列表 - 优化版（优先非OCR方案）

        Args:
            screenshot: 微信窗口截图

        Returns:
            联系人信息列表，每个元素包含name, coord, confidence等信息
        """
        try:
            self.logger.info("🔍 开始优化的联系人识别流程...")

            contacts = []

            # 优先方案1：轮廓检测识别联系人条目（快速，无需外部依赖）
            contour_contacts = self._find_contacts_by_contour_optimized(screenshot)
            if contour_contacts:
                contacts.extend(contour_contacts)
                self.logger.info(f"✅ 轮廓检测识别到 {len(contour_contacts)} 个联系人")

            # 优先方案2：专门的联系人列表检测（重点识别最后一个）
            list_contacts = self._find_contacts_by_list_items(screenshot)
            if list_contacts:
                # 合并结果，避免重复
                contacts = self._merge_contact_results(contacts, list_contacts)
                self.logger.info(f"✅ 列表检测识别到 {len(list_contacts)} 个联系人")

            # 备选方案：OCR文字识别（仅在前两种方案结果不足时使用）
            if len(contacts) < 2:  # 如果识别到的联系人少于2个，尝试OCR
                self.logger.info("🔍 联系人识别结果不足，尝试OCR补充...")
                try:
                    ocr_contacts = self._find_contacts_by_ocr_fast(screenshot)
                    if ocr_contacts:
                        contacts = self._merge_contact_results(contacts, ocr_contacts)
                        self.logger.info(f"✅ OCR补充识别到 {len(ocr_contacts)} 个联系人")
                except Exception as ocr_error:
                    self.logger.warning(f"⚠️ OCR识别跳过: {ocr_error}")

            # 过滤和排序结果
            filtered_contacts = self._filter_and_sort_contacts_optimized(contacts)

            self.logger.info(f"🎯 最终识别到 {len(filtered_contacts)} 个有效联系人")

            # 保存调试信息
            if self.debug_mode:
                self._save_contact_debug_info(screenshot, filtered_contacts)

            return filtered_contacts

        except Exception as e:
            self.logger.error(f"❌ 联系人识别失败: {e}")
            return []

    def _find_contacts_by_contour_optimized(self, screenshot: np.ndarray) -> List[Dict]:
        """优化的轮廓检测联系人识别 - 专注检测联系人文字，排除按钮"""
        try:
            self.logger.info("🔍 开始优化轮廓检测（专门针对联系人列表）...")

            # 直接使用专门的联系人列表检测
            list_contacts = self._find_contacts_by_list_items(screenshot)

            if len(list_contacts) > 0:
                self.logger.info(f"✅ 联系人列表检测成功找到 {len(list_contacts)} 个条目")
                return list_contacts

            self.logger.warning("⚠️ 联系人列表检测未找到联系人")
            return []

        except Exception as e:
            self.logger.error(f"❌ 优化轮廓检测失败: {e}")
            return []

    def _find_contacts_by_list_items(self, screenshot: np.ndarray) -> List[Dict]:
        """专门检测微信联系人列表条目的轮廓检测（重点识别最后一个联系人）"""
        try:
            self.logger.info("📋 开始联系人列表条目检测...")

            contacts = []
            height, width = screenshot.shape[:2]

            # 专门针对联系人列表的识别区域（左侧列表区域）
            roi_x = 0
            roi_y = 0
            roi_w = int(width * 0.4)  # 左侧40%宽度，专注列表区域
            roi_h = height  # 整个高度

            roi = screenshot[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
            self.logger.info(f"📐 联系人列表识别区域: ({roi_x}, {roi_y}) 尺寸: {roi_w}x{roi_h}")

            # 转换为灰度图
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 使用边缘检测来找到条目边界
            # 先进行高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # 使用Canny边缘检测
            edges = cv2.Canny(blurred, 30, 100)

            # 水平形态学操作，连接同一行的边缘
            kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 1))
            edges_horizontal = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_horizontal)

            # 垂直形态学操作，连接条目的上下边界
            kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 5))
            edges_processed = cv2.morphologyEx(edges_horizontal, cv2.MORPH_CLOSE, kernel_vertical)

            # 保存调试图像
            if self.debug_mode:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                cv2.imwrite(f"screenshots/list_edges_{timestamp}.png", edges_processed)

            # 查找轮廓
            contours, _ = cv2.findContours(edges_processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            self.logger.info(f"🔍 找到 {len(contours)} 个轮廓")

            # 创建轮廓标记图像用于调试 - 只标记选中的最后一个轮廓
            if self.debug_mode:
                contour_debug_img = cv2.cvtColor(roi, cv2.COLOR_BGR2RGB)

                # 收集所有有效轮廓
                debug_valid_contours = []
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    if self._is_contact_list_item(x, y, w, h, roi_w, roi_h):
                        debug_valid_contours.append({
                            'bbox': (x, y, w, h),
                            'y_coord': y
                        })

                # 按Y坐标排序并只标记最后一个
                if debug_valid_contours:
                    debug_valid_contours.sort(key=lambda x: x['y_coord'])
                    last_contour = debug_valid_contours[-1]
                    x, y, w, h = last_contour['bbox']

                    # 只绘制选中的最后一个轮廓（绿色粗框）
                    color = (0, 255, 0)  # 绿色
                    thickness = 3
                    cv2.rectangle(contour_debug_img, (x, y), (x + w, y + h), color, thickness)

                    # 添加标签
                    label = f"SELECTED:{w}x{h}"
                    cv2.putText(contour_debug_img, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 2)

                # 保存标记后的图像
                debug_path = f"screenshots/selected_contact_{timestamp}.png"
                cv2.imwrite(debug_path, cv2.cvtColor(contour_debug_img, cv2.COLOR_RGB2BGR))
                self.logger.info(f"💾 保存选中联系人标记图像: {debug_path}")

            # 过滤和识别联系人条目 - 收集所有符合条件的轮廓
            valid_contours = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # 检查是否符合联系人条目特征
                if self._is_contact_list_item(x, y, w, h, roi_w, roi_h):
                    # 计算置信度
                    confidence = self._calculate_list_item_confidence(w, h, roi_w)

                    valid_contours.append({
                        'contour': contour,
                        'bbox': (x, y, w, h),
                        'confidence': confidence,
                        'y_coord': y  # 用于排序
                    })

                    self.logger.debug(f"📋 有效轮廓: 位置({x}, {y}) 尺寸{w}x{h} 置信度{confidence}")

            # 按Y坐标排序（从上到下）
            valid_contours.sort(key=lambda x: x['y_coord'])

            self.logger.info(f"🔍 找到 {len(valid_contours)} 个有效轮廓")

            # 只保留最后一个轮廓（最下方的条目）
            if valid_contours:
                last_contour = valid_contours[-1]  # 最后一个（最下方）
                x, y, w, h = last_contour['bbox']
                confidence = last_contour['confidence']

                # 转换为全局坐标
                global_x = roi_x + x + w // 2
                global_y = roi_y + y + h // 2

                contacts.append({
                    'name': '联系人',  # 通常最下方的条目是"联系人"
                    'coord': (global_x, global_y),
                    'confidence': confidence,
                    'source': 'list_item_detection_last',
                    'bbox': (global_x - w//2, global_y - h//2, w, h)
                })

                self.logger.info(f"✅ 选择最下方的联系人条目: 位置({x}, {y}) 尺寸{w}x{h}")
                self.logger.info(f"📊 过滤统计: 总轮廓{len(contours)} -> 有效轮廓{len(valid_contours)} -> 最终选择1个")
            else:
                self.logger.info(f"⚠️ 未找到有效的联系人条目")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ 联系人列表检测失败: {e}")
            return []

    def _is_contact_list_item(self, x: int, y: int, w: int, h: int, roi_w: int, roi_h: int) -> bool:
        """判断轮廓是否为联系人列表条目"""
        try:
            # 1. 基本尺寸检查
            if not (30 <= w <= 400 and 10 <= h <= 80):
                self.logger.debug(f"❌ 尺寸不符: {w}x{h}")
                return False

            # 2. 宽高比检查
            aspect_ratio = w / h
            if not (1.5 <= aspect_ratio <= 20.0):
                self.logger.debug(f"❌ 宽高比不符: {aspect_ratio:.2f}")
                return False

            # 3. 位置检查
            if x < 2 or y < 5:
                self.logger.debug(f"❌ 位置太靠边: ({x}, {y})")
                return False

            # 4. 面积检查
            area = w * h
            if area < 300 or area > 25000:
                self.logger.debug(f"❌ 面积不符: {area}")
                return False

            # 5. 宽度占比检查
            width_ratio = w / roi_w
            if width_ratio < 0.2:  # 至少占20%宽度
                self.logger.debug(f"❌ 宽度占比不足: {width_ratio:.2f}")
                return False

            self.logger.debug(f"✅ 通过过滤: {w}x{h} 比例{aspect_ratio:.2f} 面积{area} 宽度占比{width_ratio:.2f}")
            return True

        except Exception as e:
            self.logger.debug(f"⚠️ 联系人条目判断异常: {e}")
            return False

    def _calculate_list_item_confidence(self, w: int, h: int, roi_w: int) -> int:
        """计算联系人列表条目的置信度"""
        try:
            confidence = 60  # 基础分数

            # 1. 尺寸评分 - 理想的联系人条目尺寸
            if 150 <= w <= 250 and 25 <= h <= 40:
                confidence += 25
            elif 120 <= w <= 300 and 20 <= h <= 45:
                confidence += 15

            # 2. 宽高比评分 - 联系人条目的理想宽高比
            aspect_ratio = w / h
            if 5.0 <= aspect_ratio <= 10.0:
                confidence += 20
            elif 3.0 <= aspect_ratio <= 12.0:
                confidence += 10

            # 3. 宽度占比评分 - 占据列表宽度的比例
            width_ratio = w / roi_w
            if 0.6 <= width_ratio <= 0.9:
                confidence += 15
            elif 0.4 <= width_ratio <= 0.95:
                confidence += 10

            return min(confidence, 100)

        except Exception as e:
            self.logger.debug(f"⚠️ 列表条目置信度计算异常: {e}")
            return 60

    def _find_contacts_by_ocr_fast(self, screenshot: np.ndarray) -> List[Dict]:
        """快速OCR联系人识别（优化版，减少依赖）"""
        try:
            self.logger.info("🔍 开始快速OCR识别...")

            contacts = []
            height, width = screenshot.shape[:2]

            # 定义通讯录区域
            roi_x = int(width * 0.05)
            roi_y = int(height * 0.15)
            roi_w = int(width * 0.6)
            roi_h = int(height * 0.7)

            # 确保ROI在图像范围内
            roi_x = max(0, min(roi_x, width - 100))
            roi_y = max(0, min(roi_y, height - 100))
            roi_w = min(roi_w, width - roi_x)
            roi_h = min(roi_h, height - roi_y)

            roi = screenshot[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

            self.logger.info(f"📐 OCR识别区域: ({roi_x}, {roi_y}) 尺寸: {roi_w}x{roi_h}")

            # 优先使用EasyOCR（如果可用）
            if EASYOCR_AVAILABLE:
                try:
                    easyocr_contacts = self._ocr_with_easyocr_fast(roi, roi_x, roi_y)
                    contacts.extend(easyocr_contacts)
                    self.logger.info(f"✅ EasyOCR识别到 {len(easyocr_contacts)} 个联系人")
                except Exception as e:
                    self.logger.warning(f"⚠️ EasyOCR快速识别失败: {e}")

            # 备选使用Tesseract（如果EasyOCR不可用或结果不足）
            if len(contacts) < 2 and OCR_AVAILABLE:
                try:
                    tesseract_contacts = self._ocr_with_tesseract_fast(roi, roi_x, roi_y)
                    contacts.extend(tesseract_contacts)
                    self.logger.info(f"✅ Tesseract识别到 {len(tesseract_contacts)} 个联系人")
                except Exception as e:
                    self.logger.warning(f"⚠️ Tesseract快速识别失败: {e}")

            # 如果都不可用，返回空列表
            if not EASYOCR_AVAILABLE and not OCR_AVAILABLE:
                self.logger.warning("⚠️ 无可用OCR引擎，跳过OCR识别")
                return []

            return contacts[:5]  # 限制最多5个

        except Exception as e:
            self.logger.error(f"❌ 快速OCR识别失败: {e}")
            return []

    def _ocr_with_easyocr_fast(self, roi: np.ndarray, offset_x: int, offset_y: int) -> List[Dict]:
        """快速EasyOCR识别（优化版）"""
        try:
            # 初始化EasyOCR读取器（使用更快的配置）
            reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 禁用GPU加速以提高兼容性

            # 进行OCR识别
            results = reader.readtext(roi, detail=1, paragraph=False)

            contacts = []
            for (bbox, text, confidence) in results:
                text = text.strip()

                if text and float(confidence) > 0.4:  # 稍微提高置信度阈值
                    # 检查是否为有效的联系人名称
                    if self._is_valid_contact_name_fast(text):
                        # 计算边界框中心点
                        bbox_array = np.array(bbox)
                        center_x = int(bbox_array[:, 0].mean()) + offset_x
                        center_y = int(bbox_array[:, 1].mean()) + offset_y

                        # 计算边界框
                        min_x = int(bbox_array[:, 0].min()) + offset_x
                        min_y = int(bbox_array[:, 1].min()) + offset_y
                        max_x = int(bbox_array[:, 0].max()) + offset_x
                        max_y = int(bbox_array[:, 1].max()) + offset_y

                        contacts.append({
                            'name': text,
                            'coord': (center_x, center_y),
                            'confidence': int(confidence * 100),
                            'source': 'easyocr_fast',
                            'bbox': (min_x, min_y, max_x - min_x, max_y - min_y)
                        })

                        self.logger.debug(f"✅ EasyOCR快速识别: '{text}' 置信度:{confidence:.2f} 坐标:({center_x}, {center_y})")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ EasyOCR快速识别失败: {e}")
            return []

    def _ocr_with_tesseract_fast(self, roi: np.ndarray, offset_x: int, offset_y: int) -> List[Dict]:
        """快速Tesseract识别（优化版）"""
        try:
            # 预处理图像以提高OCR准确率
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 简化的图像增强
            enhanced = cv2.equalizeHist(gray)

            # 使用更快的OCR配置
            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'

            # 获取文字和位置信息
            data = pytesseract.image_to_data(enhanced, config=custom_config, output_type=pytesseract.Output.DICT)

            contacts = []
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])

                if text and confidence > 40:  # 稍微提高置信度阈值
                    # 检查是否为有效的联系人名称
                    if self._is_valid_contact_name_fast(text):
                        x = data['left'][i] + offset_x
                        y = data['top'][i] + offset_y
                        w = data['width'][i]
                        h = data['height'][i]

                        center_x = x + w // 2
                        center_y = y + h // 2

                        contacts.append({
                            'name': text,
                            'coord': (center_x, center_y),
                            'confidence': confidence,
                            'source': 'tesseract_fast',
                            'bbox': (x, y, w, h)
                        })

                        self.logger.debug(f"✅ Tesseract快速识别: '{text}' 置信度:{confidence} 坐标:({center_x}, {center_y})")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ Tesseract快速识别失败: {e}")
            return []

    def _is_valid_contact_name_fast(self, text: str) -> bool:
        """快速验证联系人名称（简化版）"""
        if not text or len(text.strip()) == 0:
            return False

        text = text.strip()

        # 基本长度检查
        if not (1 <= len(text) <= 15):
            return False

        # 排除明显的系统文字
        exclude_texts = ['微信', 'WeChat', '搜索', '通讯录', '新的朋友', '群聊', '设置']
        if text in exclude_texts:
            return False

        # 简化的格式检查 - 必须包含字母或汉字
        import re
        if re.search(r'[\u4e00-\u9fa5a-zA-Z]', text):
            return True

        return False

    # ==================== 辅助功能 ====================

    def _merge_contact_results(self, contacts1: List[Dict], contacts2: List[Dict]) -> List[Dict]:
        """合并不同方法的联系人识别结果，去除重复"""
        try:
            merged = contacts1.copy()

            for contact2 in contacts2:
                coord2 = contact2['coord']
                is_duplicate = False

                # 检查是否与已有联系人重复（距离阈值30像素）
                for contact1 in merged:
                    coord1 = contact1['coord']
                    distance = ((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)**0.5

                    if distance < 30:
                        is_duplicate = True
                        # 如果新联系人有更好的置信度或更详细的信息，则替换
                        if (contact2.get('confidence', 0) > contact1.get('confidence', 0) or
                            (contact2.get('name', '').startswith('联系人_') == False and
                             contact1.get('name', '').startswith('联系人_'))):
                            # 替换为更好的结果
                            merged[merged.index(contact1)] = contact2
                        break

                if not is_duplicate:
                    merged.append(contact2)

            return merged

        except Exception as e:
            self.logger.error(f"❌ 合并联系人结果失败: {e}")
            return contacts1

    def _filter_and_sort_contacts_optimized(self, contacts: List[Dict]) -> List[Dict]:
        """优化的联系人过滤和排序"""
        try:
            if not contacts:
                return []

            # 1. 基本过滤
            filtered = []
            for contact in contacts:
                coord = contact.get('coord')
                confidence = contact.get('confidence', 0)

                # 基本有效性检查
                if coord and len(coord) == 2 and confidence > 20:
                    filtered.append(contact)

            # 2. 去重（基于坐标相近性）
            deduplicated = []
            for contact in filtered:
                coord = contact['coord']
                is_duplicate = False

                for existing in deduplicated:
                    existing_coord = existing['coord']
                    distance = ((coord[0] - existing_coord[0]) ** 2 + (coord[1] - existing_coord[1]) ** 2) ** 0.5

                    if distance < 25:  # 25像素内认为重复
                        is_duplicate = True
                        # 保留置信度更高的
                        if contact['confidence'] > existing['confidence']:
                            deduplicated.remove(existing)
                            deduplicated.append(contact)
                        break

                if not is_duplicate:
                    deduplicated.append(contact)

            # 3. 按置信度和来源排序
            def sort_key(contact):
                confidence = contact.get('confidence', 0)
                source = contact.get('source', '')

                # 来源权重：轮廓检测 > 模板匹配 > OCR
                source_weight = 0
                if 'contour' in source:
                    source_weight = 100
                elif 'template' in source:
                    source_weight = 80
                elif 'ocr' in source:
                    source_weight = 60

                return confidence + source_weight

            deduplicated.sort(key=sort_key, reverse=True)

            # 4. 限制数量
            final_contacts = deduplicated[:8]  # 最多8个联系人

            self.logger.info(f"🔍 联系人过滤结果: 原始{len(contacts)} -> 过滤{len(filtered)} -> 去重{len(deduplicated)} -> 最终{len(final_contacts)}")

            return final_contacts

        except Exception as e:
            self.logger.error(f"❌ 联系人过滤排序失败: {e}")
            return contacts[:5]  # 出错时返回前5个

    def _save_contact_debug_info(self, screenshot: np.ndarray, contacts: List[Dict]):
        """保存联系人识别的调试信息"""
        try:
            if not self.debug_mode:
                return

            # 在截图上标记识别到的联系人
            debug_img = screenshot.copy()

            for i, contact in enumerate(contacts):
                coord = contact['coord']
                bbox = contact.get('bbox', (coord[0]-20, coord[1]-10, 40, 20))
                name = contact.get('name', f'联系人{i+1}')
                confidence = contact.get('confidence', 0)
                source = contact.get('source', 'unknown')

                # 绘制边界框
                x, y, w, h = bbox
                cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 255, 0), 2)

                # 绘制中心点
                cv2.circle(debug_img, coord, 5, (255, 0, 0), -1)

                # 添加标签（包含来源信息）
                label = f"{i+1}.{name}({confidence}%)[{source}]"
                cv2.putText(debug_img, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX,
                           0.5, (255, 255, 0), 1)

            # 保存调试图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_filename = f"contact_recognition_debug_{timestamp}.png"
            debug_path = os.path.join(self.screenshot_dir, debug_filename)

            cv2.imwrite(debug_path, debug_img)
            self.logger.info(f"💾 联系人识别调试图像已保存: {debug_path}")

        except Exception as e:
            self.logger.error(f"❌ 保存调试信息失败: {e}")

    def click_contact_by_image_recognition(self, contact_name: Optional[str] = None) -> bool:
        """
        使用图像识别点击联系人

        Args:
            contact_name: 指定要点击的联系人名称，如果为None则点击第一个识别到的联系人

        Returns:
            bool: 点击是否成功
        """
        try:
            self.logger.info(f"🔍 开始使用图像识别点击联系人: {contact_name or '第一个联系人'}")

            # 获取微信窗口截图
            screenshot = self.capture_wechat_window()
            if screenshot is None:
                self.logger.error("❌ 无法获取微信窗口截图")
                return False

            # 识别联系人
            contacts = self.find_contacts_in_screenshot(screenshot)
            if not contacts:
                self.logger.warning("⚠️ 未识别到任何联系人")
                return False

            # 选择要点击的联系人
            target_contact = None

            if contact_name:
                # 查找指定名称的联系人
                for contact in contacts:
                    if contact_name in contact.get('name', ''):
                        target_contact = contact
                        break

                if not target_contact:
                    self.logger.warning(f"⚠️ 未找到指定联系人: {contact_name}，将点击第一个联系人")
                    target_contact = contacts[0]
            else:
                # 点击第一个联系人
                target_contact = contacts[0]

            # 执行点击
            coord = target_contact['coord']
            name = target_contact.get('name', '未知联系人')

            self.logger.info(f"🎯 准备点击联系人: '{name}' 坐标: {coord}")

            # 点击联系人
            pyautogui.click(coord[0], coord[1])
            time.sleep(0.5)

            self.logger.info(f"✅ 成功点击联系人: '{name}'")
            return True

        except Exception as e:
            self.logger.error(f"❌ 图像识别点击联系人失败: {e}")
            return False

    def click_last_contact(self, contact: Dict) -> bool:
        """
        点击最后一个联系人（专门用于点击检测到的最后一个联系人）

        Args:
            contact: 联系人信息字典，包含坐标等信息

        Returns:
            bool: 点击是否成功
        """
        try:
            coord = contact.get('coord')
            name = contact.get('name', '最后一个联系人')
            confidence = contact.get('confidence', 0)
            source = contact.get('source', 'unknown')

            if not coord or len(coord) != 2:
                self.logger.error("❌ 联系人坐标无效")
                return False

            self.logger.info(f"🎯 准备点击最后一个联系人: '{name}'")
            self.logger.info(f"📍 点击坐标: {coord}")
            self.logger.info(f"📊 置信度: {confidence}%")
            self.logger.info(f"🔍 来源: {source}")

            # 确保微信窗口在前台
            if not self._ensure_wechat_window_active():
                self.logger.warning("⚠️ 无法确保微信窗口激活，但继续执行点击")

            # 等待窗口稳定
            time.sleep(0.3)

            # 执行点击
            pyautogui.click(coord[0], coord[1])
            self.logger.info(f"🖱️ 已执行点击操作")

            # 等待点击响应
            time.sleep(0.5)

            # 验证点击效果（可选）
            self.logger.info(f"✅ 成功点击最后一个联系人: '{name}'")

            # 保存点击后的截图用于调试
            if self.debug_mode:
                try:
                    post_click_screenshot = self.capture_wechat_window()
                    if post_click_screenshot is not None:
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        debug_path = os.path.join(self.screenshot_dir, f"after_click_{timestamp}.png")
                        cv2.imwrite(debug_path, post_click_screenshot)
                        self.logger.info(f"💾 保存点击后截图: {debug_path}")
                except Exception as debug_e:
                    self.logger.warning(f"⚠️ 保存点击后截图失败: {debug_e}")

            return True

        except Exception as e:
            self.logger.error(f"❌ 点击最后一个联系人失败: {e}")
            return False

    def click_last_contact_auto(self) -> bool:
        """
        自动检测并点击最后一个联系人（完整流程）

        Returns:
            bool: 整个流程是否成功
        """
        try:
            self.logger.info("🚀 开始自动检测并点击最后一个联系人流程...")

            # 步骤1：获取截图
            screenshot = self.capture_wechat_window()
            if screenshot is None:
                self.logger.error("❌ 无法获取微信窗口截图")
                return False

            # 步骤2：检测最后一个联系人
            contacts = self._find_contacts_by_list_items(screenshot)
            if not contacts:
                self.logger.warning("⚠️ 未检测到任何联系人")
                return False

            # 步骤3：找到最后一个联系人
            last_contact = None
            for contact in contacts:
                if 'last' in contact.get('source', ''):
                    last_contact = contact
                    break

            if not last_contact:
                # 如果没有明确标记为last的，取第一个（因为我们的检测只返回最后一个）
                last_contact = contacts[0]

            # 步骤4：点击最后一个联系人
            return self.click_last_contact(last_contact)

        except Exception as e:
            self.logger.error(f"❌ 自动检测并点击最后一个联系人失败: {e}")
            return False


# ==================== 测试和主函数 ====================

def test_contact_recognition():
    """测试联系人识别功能"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    print("🚀 开始测试微信联系人图像识别功能...")
    print("🧹 正在清理调试图像...")

    # 创建图像识别实例（会自动清理screenshots目录）
    detector = WeChatContactDetector(debug_mode=True)

    # 测试联系人识别
    print("\n📋 测试联系人识别...")
    screenshot = detector.capture_wechat_window()
    if screenshot is not None:
        contacts = detector.find_contacts_in_screenshot(screenshot)
        print(f"识别到 {len(contacts)} 个联系人:")
        for i, contact in enumerate(contacts):
            print(f"  {i+1}. {contact.get('name', '未知')} - {contact.get('coord')} ({contact.get('source')})")

    # 测试点击联系人
    print("\n🎯 测试点击联系人...")
    success = detector.click_contact_by_image_recognition()

    if success:
        print("✅ 联系人图像识别和点击测试成功！")
    else:
        print("❌ 联系人图像识别和点击测试失败！")


def test_cleaned_detection_main(interactive: bool = True):
    """主要的清理后检测测试（来自detection.py）"""
    print("🚀 清理后的联系人检测测试")
    print("🎯 目标: 验证清理后的功能")
    print("🧹 特点: 删除无用方法，只标记绿色粗框")
    print("=" * 60)
    print("🧹 正在清理调试图像...")

    auto_click = True  # 默认启用自动点击

    if interactive:
        # 询问用户是否要自动点击
        print("\n🖱️ 是否在检测到最后一个联系人后自动点击？")
        print("  输入 'y' 或 'yes' 启用自动点击")
        print("  输入其他任意键仅进行检测（不点击）")
        user_input = input("请选择: ").strip().lower()
        auto_click = user_input in ['y', 'yes', '是', '确定']

        if auto_click:
            print("✅ 已启用自动点击功能")
        else:
            print("ℹ️ 仅进行检测，不会自动点击")
    else:
        print("🤖 自动化模式：已启用自动点击功能")

    # 创建检测器实例（会自动清理screenshots目录）
    detector = WeChatContactDetector(debug_mode=True)

    # 执行测试
    result = detector.test_cleaned_detection(auto_click=auto_click)

    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)

    print(f"  清理后检测: {'✅ 通过' if result else '❌ 失败'}")

    if result:
        print("🎉 清理后的功能正常工作！")
        print("\n💡 清理成果:")
        print("  🧹 删除了简单轮廓检测方法")
        print("  🧹 删除了改进轮廓检测方法")
        print("  🧹 删除了调试轮廓检测方法")
        print("  🧹 简化了主要轮廓检测逻辑")
        print("  🎨 只标记绿色粗框（选中的最后一个条目）")
        print("  📊 只返回最下方的联系人条目")
    else:
        print("❌ 清理后功能有问题")
        print("\n🔍 可能需要:")
        print("  - 检查代码清理是否正确")
        print("  - 验证核心功能是否保留")
        print("  - 分析调试图像")

    print("\n📁 查看调试图像:")
    print("  📸 selected_contact_*.png - 只标记选中的联系人")
    print("  🎨 只显示绿色粗框标记最后一个条目")

    print("\n💡 功能说明:")
    print("  - 现在只保留核心的联系人列表检测方法")
    print("  - 只返回最下方的联系人条目（通常是'联系人'分类）")
    print("  - 调试图像只显示选中的条目，不显示其他轮廓")


def test_auto_click_last_contact(interactive: bool = True):
    """测试自动检测并点击最后一个联系人"""
    print("🖱️ 自动检测并点击最后一个联系人")
    print("=" * 50)
    print("🎯 目标: 自动找到并点击微信通讯录中的最后一个联系人")
    print("⚠️ 注意: 此操作会实际执行鼠标点击，请确保微信窗口已打开")

    if interactive:
        # 确认用户同意执行点击操作
        print("\n⚠️ 确认操作:")
        print("  此模式将自动点击微信界面中的最后一个联系人")
        print("  请确保微信已打开并显示通讯录界面")
        confirm = input("  确定要继续吗？(输入 'yes' 确认): ").strip().lower()

        if confirm != 'yes':
            print("❌ 操作已取消")
            return
    else:
        print("🤖 自动化模式：跳过用户确认，直接执行")

    print("🧹 正在清理调试图像...")

    try:
        # 创建检测器实例
        detector = WeChatContactDetector(debug_mode=True)

        print("\n🚀 开始自动检测并点击流程...")

        # 执行自动检测并点击
        success = detector.click_last_contact_auto()

        print("\n" + "=" * 50)
        print("📊 执行结果")
        print("=" * 50)

        if success:
            print("🎉 成功！已自动检测并点击最后一个联系人")
            print("✅ 操作完成，请查看微信界面确认结果")
        else:
            print("❌ 失败！无法检测或点击最后一个联系人")
            print("🔍 请检查:")
            print("  - 微信是否已打开并可见")
            print("  - 是否在通讯录界面")
            print("  - 是否有联系人可点击")

        print("\n📁 调试信息:")
        print("  - 查看 screenshots/ 目录中的调试图像")
        print("  - 查看日志文件了解详细信息")

    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


def test_auto_detection_and_click():
    """完全自动化的检测和点击测试（无用户交互）"""
    print("🤖 完全自动化模式")
    print("=" * 50)
    print("🎯 目标: 自动检测并点击最后一个联系人（无用户交互）")
    print("🚀 开始执行自动化流程...")

    try:
        # 创建检测器实例（自动清理调试图像）
        detector = WeChatContactDetector(debug_mode=True)

        print("\n📸 正在获取微信窗口截图...")
        screenshot = detector.capture_wechat_window()

        if screenshot is None:
            print("❌ 无法获取微信窗口截图")
            return False

        height, width = screenshot.shape[:2]
        print(f"✅ 截图成功: {width}x{height}")

        print("\n🔍 正在检测最后一个联系人...")
        contacts = detector._find_contacts_by_list_items(screenshot)

        if not contacts:
            print("❌ 未检测到任何联系人")
            return False

        print(f"✅ 检测到 {len(contacts)} 个联系人条目")

        # 找到最后一个联系人
        last_contact = None
        for contact in contacts:
            if 'last' in contact.get('source', ''):
                last_contact = contact
                break

        if not last_contact:
            last_contact = contacts[0]  # 如果没有明确标记，取第一个

        name = last_contact.get('name', '联系人')
        coord = last_contact.get('coord', (0, 0))
        confidence = last_contact.get('confidence', 0)

        print(f"\n📋 最后一个联系人信息:")
        print(f"  名称: {name}")
        print(f"  坐标: {coord}")
        print(f"  置信度: {confidence}%")

        print(f"\n🖱️ 正在点击最后一个联系人...")
        click_success = detector.click_last_contact(last_contact)

        if click_success:
            print("🎉 成功！已自动检测并点击最后一个联系人")
            print("✅ 自动化流程完成")
        else:
            print("❌ 点击失败")

        return click_success

    except Exception as e:
        print(f"❌ 自动化流程异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def clean_screenshots_only():
    """仅清理screenshots目录的模式"""
    print("🧹 清理调试图像模式")
    print("=" * 50)

    try:
        # 创建临时检测器实例来使用清理功能
        detector = WeChatContactDetector(debug_mode=False)

        # 手动调用清理功能，显示详细信息
        print("🔍 正在扫描 screenshots/ 目录...")
        detector.clean_screenshots_directory(show_details=True)

        print("\n✅ 清理完成！")
        print("💡 提示: 下次运行程序时会自动清理调试图像")

    except Exception as e:
        print(f"❌ 清理失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数 - 支持不同的运行模式"""
    import sys

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    print("🚀 微信联系人检测器 - 合并版本")
    print("=" * 50)
    print("📋 可用的运行模式:")
    print("  1. python wechat_contact_detector.py auto       - 🤖 完全自动化模式（推荐）")
    print("  2. python wechat_contact_detector.py contact    - 测试联系人识别功能")
    print("  3. python wechat_contact_detector.py cleaned    - 测试清理后的检测功能（交互式）")
    print("  4. python wechat_contact_detector.py click      - 自动检测并点击（交互式）")
    print("  5. python wechat_contact_detector.py clean      - 仅清理调试图像")
    print("  6. python wechat_contact_detector.py            - 默认模式（自动化）")
    print("=" * 50)
    print("🧹 注意: 程序启动时会自动清理 screenshots/ 目录中的所有调试图像")
    print("🤖 推荐: 使用 'auto' 模式实现完全自动化，无需手动确认")

    # 根据命令行参数选择运行模式
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == "auto":
            print("\n🤖 启动完全自动化模式...")
            test_auto_detection_and_click()
        elif mode == "contact":
            print("\n🔍 启动联系人识别测试模式...")
            test_contact_recognition()
        elif mode == "cleaned":
            print("\n🧹 启动清理后检测测试模式（交互式）...")
            test_cleaned_detection_main(interactive=True)
        elif mode == "click":
            print("\n🖱️ 启动自动点击模式（交互式）...")
            test_auto_click_last_contact(interactive=True)
        elif mode == "clean":
            print("\n🧹 启动清理模式...")
            clean_screenshots_only()
        else:
            print(f"\n⚠️ 未知模式: {mode}，使用默认自动化模式")
            test_auto_detection_and_click()
    else:
        print("\n� 启动默认模式（完全自动化）...")
        test_auto_detection_and_click()

    print("\n💡 提示:")
    print("  - 确保微信窗口已打开并可见")
    print("  - 调试图像保存在 screenshots/ 目录中")
    print("  - 日志文件保存为 wechat_contact_detector_YYYYMMDD.log")


if __name__ == "__main__":
    main()
