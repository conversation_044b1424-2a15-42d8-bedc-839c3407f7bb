#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通讯录管理操作示例
功能：演示如何点击通讯录按钮后再点击通讯录管理
"""

import logging
import time
from main_interface import WeChatMainInterface

def contacts_management_flow():
    """通讯录管理操作流程"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 开始执行通讯录管理操作流程...")
    
    try:
        # 创建主界面操作实例
        interface = WeChatMainInterface()
        
        # 步骤1: 点击通讯录按钮
        logger.info("📋 步骤1: 点击通讯录按钮")
        result1 = interface.click_contacts_button()
        if result1:
            logger.info("✅ 通讯录按钮点击成功")
        else:
            logger.warning("⚠️ 通讯录按钮点击失败，但继续执行")
        
        # 等待界面加载
        logger.info("⏳ 等待通讯录界面加载...")
        time.sleep(2.0)
        
        # 步骤2: 点击通讯录管理按钮
        logger.info("📋 步骤2: 点击通讯录管理按钮")
        logger.info("🎯 使用UI树信息:")
        logger.info("  - ClassName: mmui::ContactsCellMangerBtnView")
        logger.info("  - ControlType: ControlType.ListItem")
        logger.info("  - BoundingRectangle: (61, 68, 210, 37)")
        logger.info("  - 计算的中心点坐标: (135, 85)")

        result2 = interface.click_contacts_management_button()
        if result2:
            logger.info("✅ 通讯录管理按钮点击成功")
        else:
            logger.warning("⚠️ 通讯录管理按钮点击失败")

        # 步骤3: 移动和调整通讯录管理窗口
        logger.info("📋 步骤3: 移动和调整通讯录管理窗口")
        logger.info("🎯 目标位置: (0, 0) - 屏幕左上角")
        logger.info("📏 目标尺寸: 700 x 1000 像素")

        result3 = interface.move_contacts_management_window()
        if result3:
            logger.info("✅ 通讯录管理窗口调整成功")
        else:
            logger.warning("⚠️ 通讯录管理窗口调整失败")
        
        logger.info("🎉 通讯录管理操作流程执行完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 通讯录管理操作流程异常: {e}")
        return False

def test_individual_buttons():
    """单独测试各个按钮"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 开始单独测试各个按钮...")
    
    interface = WeChatMainInterface()
    
    # 测试通讯录管理按钮的多种点击方法
    logger.info("🎯 测试通讯录管理按钮的多种点击方法:")
    
    # 方法1: 直接使用UI树坐标
    logger.info("方法1: 使用UI树边界矩形计算的中心点")
    ui_center_x = 61 + 210 // 2  # x + width/2
    ui_center_y = 68 + 37 // 2   # y + height/2
    logger.info(f"UI树中心点: ({ui_center_x}, {ui_center_y})")
    
    # 方法2: 使用配置文件坐标
    coord = interface._get_coordinate("通讯录管理按钮")
    if coord:
        logger.info(f"方法2: 配置文件坐标: {coord}")
    
    # 方法3: 动态查找
    logger.info("方法3: 动态查找通讯录管理按钮")
    dynamic_coord = interface._find_contacts_management_button()
    if dynamic_coord:
        logger.info(f"动态查找结果: {dynamic_coord}")
    else:
        logger.warning("动态查找未找到通讯录管理按钮")
    
    # 执行实际点击测试
    logger.info("🚀 执行实际点击测试...")
    result = interface.click_contacts_management_button()
    
    if result:
        logger.info("✅ 通讯录管理按钮测试成功")
    else:
        logger.warning("⚠️ 通讯录管理按钮测试失败")
    
    return result

if __name__ == "__main__":
    print("=" * 60)
    print("通讯录管理操作示例")
    print("=" * 60)
    
    # 选择执行模式
    mode = input("请选择执行模式:\n1. 完整流程 (通讯录 -> 通讯录管理 -> 窗口调整)\n2. 单独测试通讯录管理按钮\n请输入 1 或 2: ").strip()
    
    if mode == "1":
        print("\n🚀 执行完整流程...")
        contacts_management_flow()
    elif mode == "2":
        print("\n🧪 单独测试通讯录管理按钮...")
        test_individual_buttons()
    else:
        print("\n❌ 无效选择，默认执行完整流程...")
        contacts_management_flow()
    
    print("\n" + "=" * 60)
    print("操作完成")
    print("=" * 60)
