#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动点击联系人脚本 - 完全自动化版本
功能：无需人工确认，自动检测窗口并执行联系人点击
"""

import logging
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from contacts_coordinate_clicker import ContactsCoordinateClicker

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('auto_click_contacts.log', encoding='utf-8')
        ]
    )

def main():
    """主函数 - 完全自动化执行"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🚀 自动联系人点击脚本")
    print("=" * 60)
    print("📋 功能: 自动检测通讯录管理窗口并点击所有联系人")
    print("📋 特点: 无需人工确认，完全自动化执行")
    print("=" * 60)
    
    try:
        # 创建坐标点击器
        clicker = ContactsCoordinateClicker()
        
        # 显示配置信息
        print("📋 配置信息:")
        print(f"  点击位置: X={clicker.contact_area['x'] + clicker.click_offset_x} (昵称列)")
        print(f"  联系人高度: {clicker.contact_item_height}px")
        print(f"  最大滚动页数: 10页")
        
        # 自动检测窗口
        print("\n🔍 自动检测通讯录管理窗口...")
        hwnd = clicker.find_contacts_management_window()
        
        if not hwnd:
            print("❌ 未找到通讯录管理窗口")
            print("\n请确保:")
            print("  1. 微信已启动")
            print("  2. 通讯录管理窗口已打开")
            print("  3. 窗口可见且未最小化")
            logger.error("未找到通讯录管理窗口")
            return False
        
        print(f"✅ 找到通讯录管理窗口 (句柄: {hwnd})")
        
        # 自动执行点击流程
        print("\n🚀 开始自动执行联系人点击...")
        print("⏳ 预计执行时间: 2-5分钟（取决于联系人数量）")
        
        result = clicker.process_all_contacts_with_scroll(max_scrolls=10)
        
        if result:
            print("\n🎉 联系人点击流程执行成功")
            logger.info("联系人点击流程执行成功")
            return True
        else:
            print("\n⚠️ 联系人点击流程执行失败")
            logger.warning("联系人点击流程执行失败")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        logger.info("用户中断操作")
        return False
    except Exception as e:
        logger.error(f"主程序异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
        return False
    
    finally:
        print("\n" + "=" * 60)
        print("📋 详细日志已保存到: auto_click_contacts.log")
        print("=" * 60)

if __name__ == "__main__":
    success = main()
    
    # 退出码
    if success:
        sys.exit(0)  # 成功
    else:
        sys.exit(1)  # 失败
