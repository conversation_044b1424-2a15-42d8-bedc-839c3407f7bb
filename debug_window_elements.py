#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试窗口元素脚本
功能：列出通讯录管理窗口的所有子元素，帮助调试
"""

import win32gui
import logging

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def find_wechat_management_window():
    """查找通讯录管理窗口"""
    management_hwnd = None
    
    def enum_windows_callback(hwnd, _):
        nonlocal management_hwnd
        try:
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                
                # 检查是否为通讯录管理窗口
                is_management_window = (
                    "通讯录管理" in window_title or
                    "联系人管理" in window_title or
                    (class_name and "ContactManager" in class_name) or
                    ("微信" in window_title and "管理" in window_title)
                )
                
                if is_management_window:
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                    
                    if width > 300 and height > 400:
                        management_hwnd = hwnd
                        print(f"✅ 找到通讯录管理窗口: '{window_title}' (类名: {class_name})")
                        print(f"   句柄: {hwnd}, 位置: {rect}")
                        return False
                        
        except Exception as e:
            print(f"⚠️ 枚举窗口异常: {e}")
        
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    return management_hwnd

def list_all_child_elements(parent_hwnd, max_depth=3, current_depth=0):
    """递归列出所有子元素"""
    if current_depth >= max_depth:
        return
    
    indent = "  " * current_depth
    
    def enum_child_proc(hwnd, _):
        try:
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd) or ""
                class_name = win32gui.GetClassName(hwnd) or ""
                rect = win32gui.GetWindowRect(hwnd)
                
                # 显示元素信息
                print(f"{indent}📋 子元素:")
                print(f"{indent}  句柄: {hwnd}")
                print(f"{indent}  类名: '{class_name}'")
                print(f"{indent}  文本: '{window_text}'")
                print(f"{indent}  位置: {rect}")
                
                # 检查是否匹配联系人项目
                if "ContactsManagerDetailCell" in class_name:
                    print(f"{indent}  🎯 这是联系人项目！")
                elif "ContactsManagerDetailView" in class_name:
                    print(f"{indent}  📋 这是联系人列表容器！")
                elif "mmui" in class_name:
                    print(f"{indent}  🔍 这是微信UI元素")
                
                print(f"{indent}  " + "-" * 40)
                
                # 递归查看子元素
                list_all_child_elements(hwnd, max_depth, current_depth + 1)
                
        except Exception as e:
            print(f"{indent}⚠️ 枚举子元素异常: {e}")
        
        return True
    
    try:
        win32gui.EnumChildWindows(parent_hwnd, enum_child_proc, None)
    except Exception as e:
        print(f"{indent}❌ 枚举子窗口失败: {e}")

def main():
    """主函数"""
    setup_logging()
    
    print("🔍 通讯录管理窗口元素调试工具")
    print("=" * 60)
    
    # 查找通讯录管理窗口
    hwnd = find_wechat_management_window()
    
    if not hwnd:
        print("❌ 未找到通讯录管理窗口")
        print("请确保:")
        print("1. 微信已启动")
        print("2. 通讯录管理窗口已打开")
        print("3. 窗口可见且未最小化")
        return
    
    print(f"\n🔍 开始列出窗口 {hwnd} 的所有子元素...")
    print("=" * 60)
    
    # 激活窗口
    try:
        win32gui.SetForegroundWindow(hwnd)
        print("✅ 窗口已激活")
    except Exception as e:
        print(f"⚠️ 激活窗口失败: {e}")
    
    # 列出所有子元素
    list_all_child_elements(hwnd, max_depth=3)
    
    print("\n" + "=" * 60)
    print("🔍 元素列表完成")
    print("请查找包含以下关键词的元素:")
    print("  - ContactsManagerDetailCell (联系人项目)")
    print("  - ContactsManagerDetailView (联系人列表)")
    print("  - mmui (微信UI元素)")
    print("=" * 60)

if __name__ == "__main__":
    main()
