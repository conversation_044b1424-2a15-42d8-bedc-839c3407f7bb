#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的通讯录管理工作流程脚本
功能：执行完整流程 - 点击操作 → 窗口移动 → 获取联系人列表 → 循环点击联系人
"""

import logging
import time
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from main_interface import WeChatMainInterface
from contacts_list_manager import ContactsListManager

class CompleteContactsWorkflow:
    """完整的通讯录管理工作流程类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.main_interface = WeChatMainInterface()
        self.contacts_manager = ContactsListManager()
        
        self.logger.info("✅ 完整工作流程管理器初始化完成")
    
    def execute_complete_workflow(self) -> bool:
        """执行完整的工作流程"""
        try:
            self.logger.info("🚀 开始执行完整的通讯录管理工作流程...")
            
            # 步骤1-4: 执行主界面操作流程（包含窗口移动）
            self.logger.info("📋 阶段1: 执行主界面操作流程")
            self.logger.info("  步骤1: 点击微信按钮")
            self.logger.info("  步骤2: 点击通讯录按钮")
            self.logger.info("  步骤3: 点击通讯录管理按钮")
            self.logger.info("  步骤4: 移动窗口到右上角并调整尺寸")
            
            result1 = self.main_interface.execute_main_interface_flow()
            if not result1:
                self.logger.warning("⚠️ 主界面操作流程失败，但继续执行")
            else:
                self.logger.info("✅ 主界面操作流程完成")
            
            # 等待窗口移动和调整完成
            self.logger.info("⏳ 等待窗口调整完成...")
            time.sleep(3.0)
            
            # 步骤5: 获取和处理联系人列表
            self.logger.info("📋 阶段2: 获取和处理联系人列表")
            self.logger.info("  步骤5: 查找通讯录管理窗口")
            self.logger.info("  步骤6: 定位联系人列表元素")
            self.logger.info("  步骤7: 获取所有联系人项目")
            self.logger.info("  步骤8: 循环点击每个联系人")
            
            result2 = self.contacts_manager.process_all_contacts()
            if not result2:
                self.logger.error("❌ 联系人处理流程失败")
                return False
            else:
                self.logger.info("✅ 联系人处理流程完成")
            
            self.logger.info("🎉 完整工作流程执行成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 完整工作流程执行异常: {e}")
            return False
    
    def execute_contacts_only(self) -> bool:
        """仅执行联系人列表操作（假设窗口已经打开并移动）"""
        try:
            self.logger.info("🔧 执行联系人列表操作...")
            
            result = self.contacts_manager.process_all_contacts()
            
            if result:
                self.logger.info("✅ 联系人列表操作完成")
            else:
                self.logger.error("❌ 联系人列表操作失败")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 联系人列表操作异常: {e}")
            return False
    
    def execute_window_setup_only(self) -> bool:
        """仅执行窗口设置（点击和移动）"""
        try:
            self.logger.info("🔧 执行窗口设置...")
            
            result = self.main_interface.execute_main_interface_flow()
            
            if result:
                self.logger.info("✅ 窗口设置完成")
            else:
                self.logger.error("❌ 窗口设置失败")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 窗口设置异常: {e}")
            return False

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('complete_contacts_workflow.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🎉 完整的通讯录管理工作流程")
    print("=" * 60)
    
    # 显示操作选项
    print("\n操作选项:")
    print("1. 完整工作流程 (点击操作 → 窗口移动 → 联系人列表操作)")
    print("2. 仅窗口设置 (点击操作 → 窗口移动)")
    print("3. 仅联系人列表操作 (假设窗口已打开)")
    print("4. 查看UI树信息")
    
    try:
        choice = input("\n请选择操作 (1-4): ").strip()
        
        workflow = CompleteContactsWorkflow()
        
        if choice == "1":
            logger.info("选择: 完整工作流程")
            print("\n🚀 执行完整工作流程...")
            print("流程: 点击操作 → 窗口移动到右上角 → 获取联系人列表 → 循环点击联系人")
            
            result = workflow.execute_complete_workflow()
            
        elif choice == "2":
            logger.info("选择: 仅窗口设置")
            print("\n🔧 执行窗口设置...")
            
            result = workflow.execute_window_setup_only()
            
        elif choice == "3":
            logger.info("选择: 仅联系人列表操作")
            print("\n📋 执行联系人列表操作...")
            print("注意: 请确保通讯录管理窗口已经打开并移动到正确位置")
            
            confirm = input("确认通讯录管理窗口已打开? (y/N): ").strip().lower()
            if confirm == 'y':
                result = workflow.execute_contacts_only()
            else:
                print("❌ 操作取消")
                result = False
                
        elif choice == "4":
            logger.info("选择: 查看UI树信息")
            print("\n📋 UI树信息:")
            print("联系人列表元素:")
            print(f"  ClassName: mmui::ContactsManagerDetailView")
            print(f"  ControlType: ControlType.List")
            print(f"  BoundingRectangle: (1400, 76, 520, 924)")
            print(f"  ControlPatterns: Invoke, Selection, Value")
            print(f"  IsKeyboardFocusable: True")
            print(f"  LocalizedControlType: 列表")
            result = True
            
        else:
            logger.warning("无效选择，默认执行完整工作流程")
            print("\n❌ 无效选择，执行完整工作流程...")
            result = workflow.execute_complete_workflow()
        
        # 显示结果
        print("\n" + "=" * 60)
        if result:
            print("🎉 操作执行成功")
            logger.info("🎉 操作执行成功")
        else:
            print("⚠️ 操作执行失败")
            logger.warning("⚠️ 操作执行失败")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        logger.info("⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"❌ 主程序异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
    
    print("=" * 60)
    print("📋 详细日志已保存到: complete_contacts_workflow.log")

if __name__ == "__main__":
    main()
