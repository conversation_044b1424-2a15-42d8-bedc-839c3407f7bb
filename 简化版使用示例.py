#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main_interface.py 简化版使用示例
功能：演示如何使用精简后的微信主界面操作模块
"""

import logging
import time
from main_interface import WeChatMainInterface

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def demo_complete_flow():
    """演示完整操作流程"""
    print("🚀 演示完整操作流程")
    print("=" * 50)
    
    # 创建实例
    interface = WeChatMainInterface()
    
    # 显示配置信息
    coords = interface.get_current_coordinates()
    print("📍 当前坐标配置:")
    for name, coord in coords.items():
        print(f"  {name}: {coord}")
    
    print("\n🎯 执行流程: 激活窗口 → 微信按钮 → 通讯录 → 通讯录管理")
    
    # 执行完整流程
    result = interface.execute_main_interface_flow()
    
    if result:
        print("✅ 完整流程执行成功")
    else:
        print("⚠️ 完整流程执行完成（部分步骤可能失败）")
    
    return result

def demo_step_by_step():
    """演示分步操作"""
    print("\n🔧 演示分步操作")
    print("=" * 50)
    
    interface = WeChatMainInterface()
    
    steps = [
        ("步骤1: 点击微信按钮", interface.click_wechat_button),
        ("步骤2: 点击通讯录按钮", interface.click_contacts_button),
        ("步骤3: 点击通讯录管理按钮", interface.click_contacts_management_button)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}")
        try:
            result = step_func()
            if result:
                print(f"✅ {step_name} 成功")
            else:
                print(f"⚠️ {step_name} 失败")
            
            # 步骤间延迟
            time.sleep(1.5)
            
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")

def demo_individual_functions():
    """演示单个功能测试"""
    print("\n🧪 演示单个功能测试")
    print("=" * 50)
    
    interface = WeChatMainInterface()
    
    # 测试坐标获取
    print("📍 测试坐标获取:")
    test_elements = ["微信按钮", "通讯录按钮", "通讯录管理按钮", "不存在的按钮"]
    
    for element in test_elements:
        coord = interface._get_coordinate(element)
        if coord:
            print(f"  ✅ {element}: {coord}")
        else:
            print(f"  ❌ {element}: 未找到")
    
    # 测试窗口验证
    print("\n🔍 测试窗口验证:")
    window_ok = interface._verify_wechat_window()
    print(f"  微信窗口状态: {'✅ 正常' if window_ok else '❌ 异常'}")
    
    # 测试界面状态
    print("\n📊 测试界面状态:")
    state = interface.verify_interface_state()
    for key, value in state.items():
        print(f"  {key}: {'✅ 正常' if value else '❌ 异常'}")

def main():
    """主函数"""
    setup_logging()
    
    print("🎉 main_interface.py 简化版使用示例")
    print("=" * 60)
    
    print("\n选择演示模式:")
    print("1. 完整操作流程")
    print("2. 分步操作演示")
    print("3. 单个功能测试")
    print("4. 运行所有演示")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            demo_complete_flow()
        elif choice == "2":
            demo_step_by_step()
        elif choice == "3":
            demo_individual_functions()
        elif choice == "4":
            print("🚀 运行所有演示...")
            demo_complete_flow()
            demo_step_by_step()
            demo_individual_functions()
        else:
            print("❌ 无效选择，默认运行完整流程")
            demo_complete_flow()
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 演示过程中发生异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 演示完成")

if __name__ == "__main__":
    main()
