#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速联系人测试脚本
功能：快速测试修改后的联系人列表功能
"""

import logging
import sys
from pathlib import Path
import win32gui

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from contacts_list_manager import ContactsListManager

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_find_contacts():
    """测试查找联系人功能"""
    logger = logging.getLogger(__name__)
    
    print("🧪 快速联系人测试")
    print("=" * 50)
    
    try:
        manager = ContactsListManager()
        
        # 1. 查找通讯录管理窗口
        print("📋 步骤1: 查找通讯录管理窗口")
        hwnd = manager.find_contacts_management_window()
        
        if hwnd:
            print(f"✅ 找到窗口，句柄: {hwnd}")
            
            # 获取窗口信息
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            rect = win32gui.GetWindowRect(hwnd)
            
            print(f"  标题: '{title}'")
            print(f"  类名: '{class_name}'")
            print(f"  位置: {rect}")
        else:
            print("❌ 未找到通讯录管理窗口")
            return False
        
        # 2. 激活窗口
        print("\n📋 步骤2: 激活通讯录管理窗口")
        try:
            win32gui.SetForegroundWindow(hwnd)
            print("✅ 窗口已激活")
        except Exception as e:
            print(f"⚠️ 激活失败: {e}")
        
        # 3. 查找联系人项目
        print("\n📋 步骤3: 查找联系人项目")
        contact_items = manager.get_contact_items(hwnd)
        
        if contact_items:
            print(f"✅ 找到 {len(contact_items)} 个联系人")
            
            # 显示前5个联系人
            for i, contact in enumerate(contact_items[:5], 1):
                name = contact.get('name', '未知')
                center = contact.get('center', (0, 0))
                rect = contact.get('rect', (0, 0, 0, 0))
                
                print(f"  {i}. '{name}'")
                print(f"     中心点: {center}")
                print(f"     边界: {rect}")
            
            if len(contact_items) > 5:
                print(f"  ... 还有 {len(contact_items) - 5} 个联系人")
                
            return True
        else:
            print("❌ 未找到任何联系人")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    print("🎉 快速联系人测试脚本")
    print("注意: 请确保通讯录管理窗口已打开")
    print("=" * 60)
    
    # 询问用户是否准备好
    ready = input("\n通讯录管理窗口是否已打开? (y/N): ").strip().lower()
    
    if ready != 'y':
        print("❌ 请先打开通讯录管理窗口，然后重新运行此脚本")
        return
    
    # 执行测试
    result = test_find_contacts()
    
    print("\n" + "=" * 60)
    if result:
        print("🎉 测试成功！联系人查找功能正常")
    else:
        print("⚠️ 测试失败，请检查通讯录管理窗口状态")
    print("=" * 60)

if __name__ == "__main__":
    main()
