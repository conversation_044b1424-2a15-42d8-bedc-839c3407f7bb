#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试点击位置脚本
功能：测试调整后的点击位置是否正确
"""

import pyautogui
import time
import logging

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_click_positions():
    """测试不同的点击位置"""
    logger = logging.getLogger(__name__)
    
    print("🧪 测试点击位置")
    print("=" * 50)
    
    # 基于UI树信息的配置
    contact_area = {
        'x': 1400,      # 列表左边界
        'y': 76,        # 列表上边界
        'width': 520,   # 列表宽度
        'height': 924   # 列表高度
    }
    
    # 根据"昵称"UI树信息调整的点击位置
    # "昵称"位置: (1460, 44) -> 偏移 = 1460 - 1400 = 60
    click_offset_x = 60
    contact_item_height = 48
    
    print("📋 配置信息:")
    print(f"  联系人列表区域: ({contact_area['x']}, {contact_area['y']}, {contact_area['width']}, {contact_area['height']})")
    print(f"  昵称列偏移: {click_offset_x}")
    print(f"  联系人项目高度: {contact_item_height}")
    
    # 计算前几个联系人的点击位置
    click_x = contact_area['x'] + click_offset_x
    start_y = contact_area['y']
    
    test_positions = []
    for i in range(5):  # 测试前5个位置
        click_y = start_y + (contact_item_height // 2) + (i * contact_item_height)
        test_positions.append((click_x, click_y))
    
    print(f"\n📐 计算的点击位置:")
    print(f"  点击X坐标: {click_x} (列表左边界 {contact_area['x']} + 偏移 {click_offset_x})")
    print(f"  前5个联系人的Y坐标:")
    
    for i, (x, y) in enumerate(test_positions, 1):
        print(f"    联系人{i}: ({x}, {y})")
    
    # 询问是否进行实际测试
    test_mode = input(f"\n选择测试模式:\n1. 仅显示位置（不点击）\n2. 移动鼠标到位置（不点击）\n3. 实际点击测试\n请选择 (1-3): ").strip()
    
    if test_mode == "1":
        print("✅ 位置计算完成，未执行点击")
        return
    
    elif test_mode == "2":
        print("\n🖱️ 移动鼠标到各个位置（不点击）...")
        for i, (x, y) in enumerate(test_positions, 1):
            print(f"移动到联系人{i}位置: ({x}, {y})")
            pyautogui.moveTo(x, y, duration=1.0)
            time.sleep(1.0)
        print("✅ 鼠标移动测试完成")
        
    elif test_mode == "3":
        confirm = input(f"\n⚠️ 确认要进行实际点击测试吗？这将点击前3个联系人位置 (y/N): ").strip().lower()
        if confirm == 'y':
            print("\n🖱️ 开始实际点击测试...")
            
            # 设置pyautogui
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.2
            
            for i, (x, y) in enumerate(test_positions[:3], 1):  # 只测试前3个
                print(f"点击联系人{i}位置: ({x}, {y})")
                
                # 移动鼠标
                pyautogui.moveTo(x, y, duration=0.5)
                time.sleep(0.2)
                
                # 点击
                pyautogui.click(x, y)
                time.sleep(1.5)  # 等待响应
                
                print(f"✅ 联系人{i}点击完成")
            
            print("✅ 实际点击测试完成")
        else:
            print("❌ 取消实际点击测试")
    
    else:
        print("❌ 无效选择")

def show_reference_info():
    """显示参考信息"""
    print("\n📋 参考信息:")
    print("=" * 50)
    print("UI树信息:")
    print('  昵称文本位置: (1460, 44, 28, 32)')
    print('  联系人列表区域: (1400, 76, 520, 924)')
    print('  联系人项目示例: "A-dswk1" 位置应该在昵称列下方')
    
    print("\n计算过程:")
    print('  昵称列X坐标: 1460')
    print('  列表左边界: 1400') 
    print('  点击偏移: 1460 - 1400 = 60')
    print('  最终点击X: 1400 + 60 = 1460')
    
    print("\n预期效果:")
    print('  点击位置应该在昵称列，能够选中联系人')
    print('  如果位置仍不准确，可以微调 click_offset_x 值')

def main():
    """主函数"""
    setup_logging()
    
    print("🎯 点击位置测试工具")
    print("=" * 60)
    
    show_reference_info()
    test_click_positions()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
    print("如果点击位置仍不准确，请调整 contacts_coordinate_clicker.py 中的 click_offset_x 值")
    print("=" * 60)

if __name__ == "__main__":
    main()
