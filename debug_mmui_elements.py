#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试MMUI元素脚本
功能：专门查找微信UI元素，特别是联系人相关的元素
"""

import win32gui
import logging

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def find_wechat_management_window():
    """查找通讯录管理窗口"""
    management_hwnd = None
    
    def enum_windows_callback(hwnd, _):
        nonlocal management_hwnd
        try:
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                
                if "通讯录管理" in window_title:
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                    
                    if width > 300 and height > 400:
                        management_hwnd = hwnd
                        print(f"✅ 找到通讯录管理窗口: '{window_title}' (类名: {class_name})")
                        print(f"   句柄: {hwnd}, 位置: {rect}")
                        return False
                        
        except Exception as e:
            print(f"⚠️ 枚举窗口异常: {e}")
        
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    return management_hwnd

def find_mmui_elements(parent_hwnd, max_depth=5, current_depth=0):
    """递归查找所有MMUI元素"""
    if current_depth >= max_depth:
        return []
    
    mmui_elements = []
    indent = "  " * current_depth
    
    def enum_child_proc(hwnd, _):
        try:
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd) or ""
                class_name = win32gui.GetClassName(hwnd) or ""
                rect = win32gui.GetWindowRect(hwnd)
                
                # 查找包含mmui的元素
                if "mmui" in class_name.lower() or "ContactsManager" in class_name:
                    element_info = {
                        'hwnd': hwnd,
                        'class_name': class_name,
                        'text': window_text,
                        'rect': rect,
                        'depth': current_depth
                    }
                    mmui_elements.append(element_info)
                    
                    print(f"{indent}🎯 找到MMUI元素:")
                    print(f"{indent}  句柄: {hwnd}")
                    print(f"{indent}  类名: '{class_name}'")
                    print(f"{indent}  文本: '{window_text}'")
                    print(f"{indent}  位置: {rect}")
                    print(f"{indent}  深度: {current_depth}")
                    
                    # 特别标记联系人相关元素
                    if "ContactsManagerDetailCell" in class_name:
                        print(f"{indent}  🎉 这是联系人项目！")
                    elif "ContactsManagerDetailView" in class_name:
                        print(f"{indent}  📋 这是联系人列表容器！")
                    
                    print(f"{indent}  " + "-" * 50)
                
                # 递归查找子元素
                child_elements = find_mmui_elements(hwnd, max_depth, current_depth + 1)
                mmui_elements.extend(child_elements)
                
        except Exception as e:
            print(f"{indent}⚠️ 枚举子元素异常: {e}")
        
        return True
    
    try:
        win32gui.EnumChildWindows(parent_hwnd, enum_child_proc, None)
    except Exception as e:
        print(f"{indent}❌ 枚举子窗口失败: {e}")
    
    return mmui_elements

def analyze_contact_elements(mmui_elements):
    """分析联系人相关元素"""
    print("\n📊 联系人元素分析:")
    print("=" * 60)
    
    contact_cells = []
    contact_views = []
    other_mmui = []
    
    for element in mmui_elements:
        class_name = element['class_name']
        
        if "ContactsManagerDetailCell" in class_name:
            contact_cells.append(element)
        elif "ContactsManagerDetailView" in class_name:
            contact_views.append(element)
        else:
            other_mmui.append(element)
    
    print(f"📋 联系人项目 (ContactsManagerDetailCell): {len(contact_cells)} 个")
    for i, cell in enumerate(contact_cells, 1):
        print(f"  {i}. '{cell['text']}' - 位置: {cell['rect']}")
    
    print(f"\n📋 联系人列表容器 (ContactsManagerDetailView): {len(contact_views)} 个")
    for i, view in enumerate(contact_views, 1):
        print(f"  {i}. '{view['text']}' - 位置: {view['rect']}")
    
    print(f"\n📋 其他MMUI元素: {len(other_mmui)} 个")
    for i, other in enumerate(other_mmui[:5], 1):  # 只显示前5个
        print(f"  {i}. '{other['class_name']}' - '{other['text']}'")
    
    if len(other_mmui) > 5:
        print(f"  ... 还有 {len(other_mmui) - 5} 个其他元素")
    
    return contact_cells, contact_views, other_mmui

def main():
    """主函数"""
    setup_logging()
    
    print("🔍 MMUI元素调试工具")
    print("=" * 60)
    
    # 查找通讯录管理窗口
    hwnd = find_wechat_management_window()
    
    if not hwnd:
        print("❌ 未找到通讯录管理窗口")
        return
    
    # 激活窗口
    try:
        win32gui.SetForegroundWindow(hwnd)
        print("✅ 窗口已激活")
    except Exception as e:
        print(f"⚠️ 激活窗口失败: {e}")
    
    print(f"\n🔍 开始递归查找MMUI元素...")
    print("=" * 60)
    
    # 查找所有MMUI元素
    mmui_elements = find_mmui_elements(hwnd, max_depth=5)
    
    print(f"\n✅ 总共找到 {len(mmui_elements)} 个MMUI元素")
    
    # 分析联系人相关元素
    contact_cells, contact_views, other_mmui = analyze_contact_elements(mmui_elements)
    
    print("\n" + "=" * 60)
    print("🎯 调试建议:")
    
    if contact_cells:
        print("✅ 找到联系人项目，脚本应该能正常工作")
    elif contact_views:
        print("⚠️ 找到联系人列表容器但没有项目，可能列表为空")
    else:
        print("❌ 没有找到联系人相关元素，可能需要:")
        print("  1. 确保通讯录管理窗口完全加载")
        print("  2. 检查是否有联系人数据")
        print("  3. 尝试滚动或刷新联系人列表")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
