#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通讯录管理窗口联系人列表操作脚本
功能：获取通讯录管理窗口的联系人列表并循环点击每个联系人
"""

import win32gui
import win32api
import time
import logging
from typing import List, Dict, Optional, Tuple
import pyautogui

class ContactsListManager:
    """通讯录管理窗口联系人列表操作类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 基于UI树信息的配置
        self.list_class_name = "mmui::ContactsManagerDetailView"  # 列表容器
        self.contact_item_class_name = "mmui::ContactsManagerDetailCell"  # 联系人项目
        self.list_control_type = "ControlType.List"
        self.item_control_type = "ControlType.ListItem"
        self.expected_bounds = (1400, 76, 520, 924)  # (x, y, width, height)
        self.contact_item_height = 48  # 单个联系人项目高度
        
        # 点击配置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.2
        
        self.logger.info("✅ 通讯录联系人列表管理器初始化完成")
    
    def find_contacts_management_window(self) -> Optional[int]:
        """查找通讯录管理窗口句柄"""
        try:
            self.logger.info("🔍 查找通讯录管理窗口...")
            
            management_hwnd = None
            
            def enum_windows_callback(hwnd, _):
                nonlocal management_hwnd
                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        class_name = win32gui.GetClassName(hwnd)
                        
                        # 检查是否为通讯录管理窗口
                        is_management_window = (
                            "通讯录管理" in window_title or
                            "联系人管理" in window_title or
                            (class_name and "ContactManager" in class_name) or
                            ("微信" in window_title and "管理" in window_title)
                        )
                        
                        if is_management_window:
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]
                            
                            if width > 300 and height > 400:
                                management_hwnd = hwnd
                                self.logger.info(f"✅ 找到通讯录管理窗口: '{window_title}' (类名: {class_name})")
                                return False
                                
                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举窗口异常: {e}")
                
                return True
            
            win32gui.EnumWindows(enum_windows_callback, None)
            return management_hwnd
            
        except Exception as e:
            self.logger.error(f"❌ 查找通讯录管理窗口失败: {e}")
            return None
    
    def find_contacts_list_element(self, parent_hwnd: int) -> Optional[int]:
        """查找联系人列表元素"""
        try:
            self.logger.info("🔍 查找联系人列表元素...")
            
            list_hwnd = None
            
            def enum_child_proc(hwnd, _):
                nonlocal list_hwnd
                try:
                    class_name = win32gui.GetClassName(hwnd) or ""
                    
                    # 根据UI树信息匹配联系人列表
                    if self.list_class_name in class_name:
                        if win32gui.IsWindowVisible(hwnd):
                            rect = win32gui.GetWindowRect(hwnd)
                            self.logger.info(f"📋 找到联系人列表: 类名='{class_name}', 位置={rect}")
                            
                            # 验证边界矩形是否匹配
                            x, y, right, bottom = rect
                            width = right - x
                            height = bottom - y
                            
                            # 允许一定的位置偏差
                            expected_x, expected_y, expected_w, expected_h = self.expected_bounds
                            if (abs(x - expected_x) <= 50 and abs(y - expected_y) <= 50 and
                                abs(width - expected_w) <= 100 and abs(height - expected_h) <= 100):
                                list_hwnd = hwnd
                                self.logger.info(f"✅ 联系人列表位置验证通过")
                                return False
                            else:
                                self.logger.info(f"⚠️ 位置不匹配，期望: {self.expected_bounds}, 实际: ({x}, {y}, {width}, {height})")
                                
                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举子窗口异常: {e}")
                
                return True
            
            win32gui.EnumChildWindows(parent_hwnd, enum_child_proc, None)
            return list_hwnd
            
        except Exception as e:
            self.logger.error(f"❌ 查找联系人列表元素失败: {e}")
            return None
    
    def get_contact_items(self, parent_hwnd: int) -> List[Dict]:
        """递归查找所有联系人项目"""
        try:
            self.logger.info("📋 获取联系人列表项目...")

            contact_items = []

            def enum_contact_items_recursive(hwnd, depth=0):
                """递归枚举联系人项目"""
                if depth > 5:  # 限制递归深度
                    return

                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd) or ""
                        class_name = win32gui.GetClassName(hwnd) or ""
                        rect = win32gui.GetWindowRect(hwnd)

                        # 根据新的UI树信息匹配联系人项目
                        if self.contact_item_class_name in class_name:
                            # 验证是否在预期的联系人列表区域内
                            if self._is_in_contact_area(rect):
                                center_x = (rect[0] + rect[2]) // 2
                                center_y = (rect[1] + rect[3]) // 2

                                contact_info = {
                                    'hwnd': hwnd,
                                    'name': window_text,
                                    'class_name': class_name,
                                    'rect': rect,
                                    'center': (center_x, center_y)
                                }
                                contact_items.append(contact_info)
                                self.logger.info(f"📋 找到联系人: '{window_text}' 坐标: ({center_x}, {center_y})")

                        # 递归查找子窗口
                        def enum_child_proc(child_hwnd, _):
                            enum_contact_items_recursive(child_hwnd, depth + 1)
                            return True

                        win32gui.EnumChildWindows(hwnd, enum_child_proc, None)

                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举联系人项目异常 (深度{depth}): {e}")

            # 开始递归查找
            enum_contact_items_recursive(parent_hwnd, 0)

            # 按Y坐标排序，确保从上到下的顺序
            contact_items.sort(key=lambda x: x['center'][1])

            self.logger.info(f"✅ 找到 {len(contact_items)} 个联系人项目")
            return contact_items

        except Exception as e:
            self.logger.error(f"❌ 获取联系人项目失败: {e}")
            return []
    
    def _is_in_contact_area(self, rect: Tuple[int, int, int, int]) -> bool:
        """检查元素是否在联系人列表区域内"""
        try:
            x, y, right, bottom = rect
            width = right - x
            height = bottom - y

            # 检查是否在预期的联系人列表区域内
            expected_x, expected_y, _, _ = self.expected_bounds

            # 放宽位置检查条件，允许更大的偏差
            # 主要检查X坐标和宽度是否合理
            if (abs(x - expected_x) <= 200 and  # 允许更大的X偏差
                y >= expected_y - 50 and  # 允许Y坐标有一定偏差
                width >= 300 and width <= 700 and  # 宽度范围检查
                height >= 30 and height <= 80):  # 高度范围检查
                self.logger.debug(f"✅ 联系人区域验证通过: 位置({x}, {y}), 尺寸({width}x{height})")
                return True
            else:
                self.logger.debug(f"⚠️ 联系人区域验证失败: 位置({x}, {y}), 尺寸({width}x{height})")
                self.logger.debug(f"   期望: X≈{expected_x}±200, Y≥{expected_y-50}, W=300-700, H=30-80")

            return False

        except Exception as e:
            self.logger.debug(f"⚠️ 检查联系人区域异常: {e}")
            return False
    
    def click_contact_item(self, contact_info: Dict) -> bool:
        """点击联系人项目"""
        try:
            name = contact_info.get('name', '未知联系人')
            center_x, center_y = contact_info.get('center', (0, 0))
            
            self.logger.info(f"🖱️ 点击联系人: '{name}' 坐标: ({center_x}, {center_y})")
            
            # 移动鼠标到联系人位置
            pyautogui.moveTo(center_x, center_y, duration=0.3)
            time.sleep(0.1)
            
            # 点击联系人
            pyautogui.click(center_x, center_y)
            time.sleep(0.5)  # 等待响应
            
            self.logger.info(f"✅ 成功点击联系人: '{name}'")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 点击联系人失败: {e}")
            return False
    
    def process_all_contacts(self) -> bool:
        """处理所有联系人的完整流程"""
        try:
            self.logger.info("🚀 开始处理所有联系人...")

            # 1. 查找通讯录管理窗口
            management_hwnd = self.find_contacts_management_window()
            if not management_hwnd:
                self.logger.error("❌ 未找到通讯录管理窗口")
                return False

            # 2. 激活通讯录管理窗口
            try:
                win32gui.SetForegroundWindow(management_hwnd)
                time.sleep(0.5)
                self.logger.info("✅ 通讯录管理窗口已激活")
            except Exception as e:
                self.logger.warning(f"⚠️ 激活窗口失败: {e}")

            # 3. 直接获取所有联系人项目（不需要查找列表容器）
            contact_items = self.get_contact_items(management_hwnd)
            if not contact_items:
                self.logger.warning("⚠️ 未找到任何联系人项目")
                return False

            # 4. 循环点击每个联系人
            self.logger.info(f"📋 开始循环点击 {len(contact_items)} 个联系人...")

            success_count = 0
            for i, contact_info in enumerate(contact_items, 1):
                name = contact_info.get('name', f'联系人{i}')
                self.logger.info(f"📋 处理第 {i}/{len(contact_items)} 个联系人: '{name}'")

                if self.click_contact_item(contact_info):
                    success_count += 1
                    # 点击间隔
                    time.sleep(1.0)
                else:
                    self.logger.warning(f"⚠️ 点击联系人 '{name}' 失败")

            self.logger.info(f"✅ 联系人处理完成: 成功 {success_count}/{len(contact_items)}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理联系人流程异常: {e}")
            return False

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('contacts_list_manager.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🎉 通讯录管理窗口联系人列表操作脚本")
    print("=" * 60)
    
    try:
        # 创建联系人列表管理器
        manager = ContactsListManager()
        
        # 显示UI树信息
        print("📋 基于UI树信息:")
        print(f"  列表容器类名: {manager.list_class_name}")
        print(f"  联系人项目类名: {manager.contact_item_class_name}")
        print(f"  控件类型: {manager.item_control_type}")
        print(f"  列表区域: {manager.expected_bounds}")
        print(f"  联系人项目高度: {manager.contact_item_height}")
        print("📋 联系人项目示例:")
        print("  Name: 'Abc珠宝小谈📿.  未刷'")
        print("  BoundingRectangle: (1400, 76, 520, 48)")
        
        # 执行联系人处理流程
        print("\n🚀 开始执行联系人处理流程...")
        result = manager.process_all_contacts()
        
        if result:
            print("\n✅ 联系人处理流程执行成功")
        else:
            print("\n⚠️ 联系人处理流程执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"❌ 主程序异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("📋 详细日志已保存到: contacts_list_manager.log")

if __name__ == "__main__":
    main()
