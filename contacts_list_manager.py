#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通讯录管理窗口联系人列表操作脚本
功能：获取通讯录管理窗口的联系人列表并循环点击每个联系人
"""

import win32gui
import win32api
import time
import logging
from typing import List, Dict, Optional, Tuple
import pyautogui

class ContactsListManager:
    """通讯录管理窗口联系人列表操作类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 基于UI树信息的配置
        self.list_class_name = "mmui::ContactsManagerDetailView"
        self.list_control_type = "ControlType.List"
        self.expected_bounds = (1400, 76, 520, 924)  # (x, y, width, height)
        
        # 点击配置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.2
        
        self.logger.info("✅ 通讯录联系人列表管理器初始化完成")
    
    def find_contacts_management_window(self) -> Optional[int]:
        """查找通讯录管理窗口句柄"""
        try:
            self.logger.info("🔍 查找通讯录管理窗口...")
            
            management_hwnd = None
            
            def enum_windows_callback(hwnd, _):
                nonlocal management_hwnd
                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        class_name = win32gui.GetClassName(hwnd)
                        
                        # 检查是否为通讯录管理窗口
                        is_management_window = (
                            "通讯录管理" in window_title or
                            "联系人管理" in window_title or
                            (class_name and "ContactManager" in class_name) or
                            ("微信" in window_title and "管理" in window_title)
                        )
                        
                        if is_management_window:
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]
                            
                            if width > 300 and height > 400:
                                management_hwnd = hwnd
                                self.logger.info(f"✅ 找到通讯录管理窗口: '{window_title}' (类名: {class_name})")
                                return False
                                
                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举窗口异常: {e}")
                
                return True
            
            win32gui.EnumWindows(enum_windows_callback, None)
            return management_hwnd
            
        except Exception as e:
            self.logger.error(f"❌ 查找通讯录管理窗口失败: {e}")
            return None
    
    def find_contacts_list_element(self, parent_hwnd: int) -> Optional[int]:
        """查找联系人列表元素"""
        try:
            self.logger.info("🔍 查找联系人列表元素...")
            
            list_hwnd = None
            
            def enum_child_proc(hwnd, _):
                nonlocal list_hwnd
                try:
                    class_name = win32gui.GetClassName(hwnd) or ""
                    
                    # 根据UI树信息匹配联系人列表
                    if self.list_class_name in class_name:
                        if win32gui.IsWindowVisible(hwnd):
                            rect = win32gui.GetWindowRect(hwnd)
                            self.logger.info(f"📋 找到联系人列表: 类名='{class_name}', 位置={rect}")
                            
                            # 验证边界矩形是否匹配
                            x, y, right, bottom = rect
                            width = right - x
                            height = bottom - y
                            
                            # 允许一定的位置偏差
                            expected_x, expected_y, expected_w, expected_h = self.expected_bounds
                            if (abs(x - expected_x) <= 50 and abs(y - expected_y) <= 50 and
                                abs(width - expected_w) <= 100 and abs(height - expected_h) <= 100):
                                list_hwnd = hwnd
                                self.logger.info(f"✅ 联系人列表位置验证通过")
                                return False
                            else:
                                self.logger.info(f"⚠️ 位置不匹配，期望: {self.expected_bounds}, 实际: ({x}, {y}, {width}, {height})")
                                
                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举子窗口异常: {e}")
                
                return True
            
            win32gui.EnumChildWindows(parent_hwnd, enum_child_proc, None)
            return list_hwnd
            
        except Exception as e:
            self.logger.error(f"❌ 查找联系人列表元素失败: {e}")
            return None
    
    def get_contact_items(self, list_hwnd: int) -> List[Dict]:
        """获取联系人列表中的所有联系人项目"""
        try:
            self.logger.info("📋 获取联系人列表项目...")
            
            contact_items = []
            
            def enum_list_items(hwnd, _):
                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd) or ""
                        class_name = win32gui.GetClassName(hwnd) or ""
                        rect = win32gui.GetWindowRect(hwnd)
                        
                        # 过滤联系人项目
                        if self._is_contact_item(window_text, class_name, rect):
                            center_x = (rect[0] + rect[2]) // 2
                            center_y = (rect[1] + rect[3]) // 2
                            
                            contact_info = {
                                'hwnd': hwnd,
                                'name': window_text,
                                'class_name': class_name,
                                'rect': rect,
                                'center': (center_x, center_y)
                            }
                            contact_items.append(contact_info)
                            self.logger.debug(f"📋 找到联系人: '{window_text}' 坐标: ({center_x}, {center_y})")
                            
                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举联系人项目异常: {e}")
                
                return True
            
            win32gui.EnumChildWindows(list_hwnd, enum_list_items, None)
            
            # 按Y坐标排序，确保从上到下的顺序
            contact_items.sort(key=lambda x: x['center'][1])
            
            self.logger.info(f"✅ 找到 {len(contact_items)} 个联系人项目")
            return contact_items
            
        except Exception as e:
            self.logger.error(f"❌ 获取联系人项目失败: {e}")
            return []
    
    def _is_contact_item(self, window_text: str, class_name: str, rect: Tuple[int, int, int, int]) -> bool:
        """判断是否为联系人项目"""
        try:
            # 检查文本内容
            if not window_text or len(window_text.strip()) == 0:
                return False
            
            text = window_text.strip()
            
            # 排除系统元素
            exclude_texts = [
                '通讯录管理', '联系人管理', '搜索', '添加', '删除', '编辑',
                '确定', '取消', '返回', '刷新', '更多', '全部', '分组'
            ]
            
            for exclude in exclude_texts:
                if text == exclude:
                    return False
            
            # 检查窗口大小（联系人项目通常有一定的尺寸）
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            if width < 50 or height < 20 or width > 500 or height > 100:
                return False
            
            # 检查是否在联系人列表区域内
            x, y = rect[0], rect[1]
            expected_x, expected_y, expected_w, expected_h = self.expected_bounds
            
            if (expected_x <= x <= expected_x + expected_w and
                expected_y <= y <= expected_y + expected_h):
                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"⚠️ 判断联系人项目异常: {e}")
            return False
    
    def click_contact_item(self, contact_info: Dict) -> bool:
        """点击联系人项目"""
        try:
            name = contact_info.get('name', '未知联系人')
            center_x, center_y = contact_info.get('center', (0, 0))
            
            self.logger.info(f"🖱️ 点击联系人: '{name}' 坐标: ({center_x}, {center_y})")
            
            # 移动鼠标到联系人位置
            pyautogui.moveTo(center_x, center_y, duration=0.3)
            time.sleep(0.1)
            
            # 点击联系人
            pyautogui.click(center_x, center_y)
            time.sleep(0.5)  # 等待响应
            
            self.logger.info(f"✅ 成功点击联系人: '{name}'")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 点击联系人失败: {e}")
            return False
    
    def process_all_contacts(self) -> bool:
        """处理所有联系人的完整流程"""
        try:
            self.logger.info("🚀 开始处理所有联系人...")
            
            # 1. 查找通讯录管理窗口
            management_hwnd = self.find_contacts_management_window()
            if not management_hwnd:
                self.logger.error("❌ 未找到通讯录管理窗口")
                return False
            
            # 2. 查找联系人列表元素
            list_hwnd = self.find_contacts_list_element(management_hwnd)
            if not list_hwnd:
                self.logger.error("❌ 未找到联系人列表元素")
                return False
            
            # 3. 获取所有联系人项目
            contact_items = self.get_contact_items(list_hwnd)
            if not contact_items:
                self.logger.warning("⚠️ 未找到任何联系人项目")
                return False
            
            # 4. 循环点击每个联系人
            self.logger.info(f"📋 开始循环点击 {len(contact_items)} 个联系人...")
            
            success_count = 0
            for i, contact_info in enumerate(contact_items, 1):
                name = contact_info.get('name', f'联系人{i}')
                self.logger.info(f"📋 处理第 {i}/{len(contact_items)} 个联系人: '{name}'")
                
                if self.click_contact_item(contact_info):
                    success_count += 1
                    # 点击间隔
                    time.sleep(1.0)
                else:
                    self.logger.warning(f"⚠️ 点击联系人 '{name}' 失败")
            
            self.logger.info(f"✅ 联系人处理完成: 成功 {success_count}/{len(contact_items)}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 处理联系人流程异常: {e}")
            return False

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('contacts_list_manager.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🎉 通讯录管理窗口联系人列表操作脚本")
    print("=" * 60)
    
    try:
        # 创建联系人列表管理器
        manager = ContactsListManager()
        
        # 显示UI树信息
        print("📋 基于UI树信息:")
        print(f"  ClassName: {manager.list_class_name}")
        print(f"  ControlType: {manager.list_control_type}")
        print(f"  BoundingRectangle: {manager.expected_bounds}")
        
        # 执行联系人处理流程
        print("\n🚀 开始执行联系人处理流程...")
        result = manager.process_all_contacts()
        
        if result:
            print("\n✅ 联系人处理流程执行成功")
        else:
            print("\n⚠️ 联系人处理流程执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"❌ 主程序异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("📋 详细日志已保存到: contacts_list_manager.log")

if __name__ == "__main__":
    main()
