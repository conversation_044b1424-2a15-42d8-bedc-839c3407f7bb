#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
位置计算验证脚本
功能：验证屏幕右上角位置计算的正确性
"""

import win32api
import logging

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_position_calculation():
    """测试位置计算逻辑"""
    logger = logging.getLogger(__name__)
    
    print("🧪 屏幕右上角位置计算验证")
    print("=" * 50)
    
    try:
        # 获取屏幕分辨率
        screen_width = win32api.GetSystemMetrics(0)  # SM_CXSCREEN
        screen_height = win32api.GetSystemMetrics(1)  # SM_CYSCREEN
        
        print(f"📺 当前屏幕分辨率: {screen_width} x {screen_height}")
        
        # 窗口尺寸
        target_width = 700
        target_height = 1000
        
        print(f"📏 目标窗口尺寸: {target_width} x {target_height}")
        
        # 计算右上角位置
        target_x = screen_width - target_width
        target_y = 0
        
        print(f"📐 计算过程:")
        print(f"  target_x = screen_width - target_width")
        print(f"  target_x = {screen_width} - {target_width} = {target_x}")
        print(f"  target_y = 0 (顶部位置)")
        
        print(f"\n🎯 最终位置: ({target_x}, {target_y}) - 屏幕右上角")
        
        # 验证位置合理性
        print(f"\n✅ 位置验证:")
        
        # 检查X坐标
        if target_x >= 0:
            print(f"  ✅ X坐标 {target_x} >= 0 (不会超出屏幕左边界)")
        else:
            print(f"  ❌ X坐标 {target_x} < 0 (窗口会超出屏幕左边界)")
            
        # 检查窗口是否完全在屏幕内
        window_right = target_x + target_width
        if window_right <= screen_width:
            print(f"  ✅ 窗口右边界 {window_right} <= 屏幕宽度 {screen_width}")
        else:
            print(f"  ❌ 窗口右边界 {window_right} > 屏幕宽度 {screen_width}")
            
        # 检查Y坐标
        window_bottom = target_y + target_height
        if window_bottom <= screen_height:
            print(f"  ✅ 窗口底边界 {window_bottom} <= 屏幕高度 {screen_height}")
        else:
            print(f"  ⚠️ 窗口底边界 {window_bottom} > 屏幕高度 {screen_height} (窗口可能超出屏幕底部)")
        
        # 不同分辨率下的示例
        print(f"\n📊 不同分辨率下的位置示例:")
        resolutions = [
            (1920, 1080, "标准1080p"),
            (2560, 1440, "2K分辨率"),
            (3840, 2160, "4K分辨率"),
            (1366, 768, "笔记本常见分辨率"),
            (1280, 720, "720p分辨率")
        ]
        
        for width, height, desc in resolutions:
            calc_x = width - target_width
            if calc_x >= 0:
                status = "✅"
            else:
                status = "❌"
            print(f"  {status} {desc} ({width}x{height}): 右上角位置 ({calc_x}, 0)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 位置计算测试失败: {e}")
        return False

def test_main_interface_integration():
    """测试与主界面模块的集成"""
    print(f"\n🔧 主界面模块集成测试")
    print("=" * 50)
    
    try:
        from main_interface import WeChatMainInterface
        
        interface = WeChatMainInterface()
        print("✅ 主界面模块导入成功")
        
        # 检查方法是否存在
        if hasattr(interface, 'move_contacts_management_window'):
            print("✅ move_contacts_management_window 方法存在")
        else:
            print("❌ move_contacts_management_window 方法不存在")
            
        if hasattr(interface, '_find_contacts_management_window'):
            print("✅ _find_contacts_management_window 方法存在")
        else:
            print("❌ _find_contacts_management_window 方法不存在")
        
        print("✅ 主界面模块集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 主界面模块集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    setup_logging()
    
    print("🎉 通讯录管理窗口位置计算验证")
    print("=" * 60)
    
    # 测试位置计算
    result1 = test_position_calculation()
    
    # 测试模块集成
    result2 = test_main_interface_integration()
    
    # 显示总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    if result1:
        print("✅ 位置计算逻辑验证通过")
    else:
        print("❌ 位置计算逻辑验证失败")
        
    if result2:
        print("✅ 主界面模块集成验证通过")
    else:
        print("❌ 主界面模块集成验证失败")
    
    if result1 and result2:
        print("\n🎉 所有测试通过！屏幕右上角位置计算功能正常")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
