#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证点击位置脚本
功能：验证调整后的点击位置计算
"""

def verify_click_position():
    """验证点击位置计算"""
    print("🎯 点击位置验证")
    print("=" * 50)
    
    # 基于UI树信息
    contact_area_x = 1400  # 列表左边界
    contact_area_y = 76    # 列表上边界
    
    # 昵称文本位置 (1460, 44)
    nickname_x = 1460
    
    # 计算偏移
    click_offset_x = nickname_x - contact_area_x
    
    print("📋 位置计算:")
    print(f"  联系人列表左边界: {contact_area_x}")
    print(f"  昵称文本X坐标: {nickname_x}")
    print(f"  计算的偏移: {nickname_x} - {contact_area_x} = {click_offset_x}")
    
    # 计算实际点击位置
    click_x = contact_area_x + click_offset_x
    
    print(f"\n🎯 点击位置:")
    print(f"  点击X坐标: {contact_area_x} + {click_offset_x} = {click_x}")
    
    # 计算前几个联系人的Y坐标
    contact_item_height = 48
    start_y = contact_area_y + (contact_item_height // 2)  # 第一个联系人中心
    
    print(f"\n📋 前5个联系人位置:")
    for i in range(5):
        y = start_y + (i * contact_item_height)
        print(f"  联系人{i+1}: ({click_x}, {y})")
    
    print(f"\n✅ 新的点击偏移值: {click_offset_x}")
    print("这个值已经更新到 contacts_coordinate_clicker.py 中")

if __name__ == "__main__":
    verify_click_position()
